spring:
  application:
    name: api-gateway
  devtools:
    restart:
      enabled: true
      poll-interval: 2s
      quiet-period: 1s
    livereload:
      enabled: true
  cloud:
    gateway:
      routes:
        - id: customer-service
          uri: http://localhost:8085
          predicates:
            - Path=/api/customer/**
        - id: job-service
          uri: http://localhost:8086
          predicates:
            - Path=/api/job/**,/api/job-category/**
        - id: customer-contract-service
          uri: http://localhost:8087
          predicates:
            - Path=/api/customer-contract/**
        - id: customer-payment-service
          uri: http://localhost:8088
          predicates:
            - Path=/api/customer-payment/**
        - id: job-detail-service
          uri: http://localhost:8087
          predicates:
            - Path=/api/job-detail/**
        - id: work-shift-service
          uri: http://localhost:8087
          predicates:
            - Path=/api/work-shift/**
        - id: customer-statistics-service
          uri: http://localhost:8089
          predicates:
            - Path=/api/customer-statistics/**
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
server:
  port: 8083

management:
  endpoints:
    web:
      exposure:
        include: "*"