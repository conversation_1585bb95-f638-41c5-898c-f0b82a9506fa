import { JobDetail } from './JobDetail';

export interface CustomerContract {
  id?: number;
  startingDate: string;
  endingDate: string;
  totalAmount: number;
  totalPaid?: number;
  address: string;
  description?: string;
  customerId: number;
  customerName?: string; // For display purposes
  jobDetails: JobDetail[];
  status?: number; // 0: Pending, 1: Active, 2: Completed, 3: Cancelled
  isDeleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export const ContractStatusMap: Record<number, string> = {
  0: 'Chờ duyệt',
  1: 'Đang hoạt động',
  2: 'Đã hoàn thành',
  3: 'Đã hủy'
};
