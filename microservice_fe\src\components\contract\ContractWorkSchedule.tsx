import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  useTheme,
  Paper,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import WorkIcon from '@mui/icons-material/Work';
import { CustomerContract } from '../../models';
import { calculateWorkingDates, formatWorkingDays } from '../../utils/workingDaysUtils';
import { formatCurrency } from '../../utils/formatters';
import { formatDateLocalized } from '../../utils/dateUtils';

// Mapping for Vietnamese day names
const dayNames: { [key: number]: string } = {
  1: 'Thứ Hai',
  2: 'Thứ Ba',
  3: '<PERSON><PERSON><PERSON>',
  4: '<PERSON><PERSON><PERSON>',
  5: '<PERSON><PERSON><PERSON>',
  6: '<PERSON><PERSON><PERSON>',
  7: '<PERSON><PERSON>'
};

interface ContractWorkScheduleProps {
  contract: CustomerContract;
}

const ContractWorkSchedule: React.FC<ContractWorkScheduleProps> = ({ contract }) => {
  const [expandedShifts, setExpandedShifts] = useState<{ [key: string]: boolean }>({});
  const theme = useTheme();

  const handleShiftExpand = (shiftKey: string) => {
    setExpandedShifts(prev => ({
      ...prev,
      [shiftKey]: !prev[shiftKey]
    }));
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <CalendarMonthIcon sx={{ mr: 1, color: theme.palette.primary.main, fontSize: 28 }} />
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
          LỊCH LÀM VIỆC CHI TIẾT
        </Typography>
      </Box>

      {contract.jobDetails.map((jobDetail, jobIndex) => {
        // Calculate total working days and amount for this job
        let totalJobWorkingDays = 0;
        let totalJobAmount = 0;

        jobDetail.workShifts.forEach(shift => {
          const workingDates = calculateWorkingDates(
            jobDetail.startDate,
            jobDetail.endDate,
            shift.workingDays
          );
          totalJobWorkingDays += workingDates.length;
          totalJobAmount += (shift.salary || 0) * (shift.numberOfWorkers || 0) * workingDates.length;
        });

        return (
          <Card
            key={jobIndex}
            elevation={3}
            sx={{
              mb: 3,
              borderRadius: '12px',
              border: '2px solid',
              borderColor: theme.palette.primary.light,
              overflow: 'hidden',
            }}
          >
            <Box
              sx={{
                p: 3,
                backgroundColor: theme.palette.primary.light,
                borderBottom: '1px solid',
                borderColor: theme.palette.primary.main,
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <WorkIcon sx={{ mr: 2, color: theme.palette.primary.main, fontSize: 32 }} />
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                      {jobDetail.jobCategoryName}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {formatDateLocalized(jobDetail.startDate)} - {formatDateLocalized(jobDetail.endDate)}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ textAlign: 'right' }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
                    {formatCurrency(totalJobAmount)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {totalJobWorkingDays} ngày làm việc
                  </Typography>
                </Box>
              </Box>
            </Box>

            <CardContent sx={{ p: 0 }}>
              {jobDetail.workShifts.map((shift, shiftIndex) => {
                const workingDates = calculateWorkingDates(
                  jobDetail.startDate,
                  jobDetail.endDate,
                  shift.workingDays
                );

                // Group dates by day of week
                const datesByDayOfWeek: { [key: number]: string[] } = {};
                workingDates.forEach(dateStr => {
                  const date = new Date(dateStr.split('/').reverse().join('-'));
                  const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay();
                  if (!datesByDayOfWeek[dayOfWeek]) {
                    datesByDayOfWeek[dayOfWeek] = [];
                  }
                  datesByDayOfWeek[dayOfWeek].push(dateStr);
                });

                const shiftAmount = (shift.salary || 0) * (shift.numberOfWorkers || 0) * workingDates.length;
                const shiftKey = `${jobIndex}-${shiftIndex}`;

                return (
                  <Accordion
                    key={shiftIndex}
                    expanded={expandedShifts[shiftKey] || false}
                    onChange={() => handleShiftExpand(shiftKey)}
                    sx={{ boxShadow: 'none', '&:before': { display: 'none' } }}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      sx={{
                        backgroundColor: theme.palette.action.hover,
                        borderBottom: '1px solid #e0e0e0',
                        minHeight: '64px',
                      }}
                    >
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center' }}>
                        <Box sx={{ minWidth: '200px', flex: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <AccessTimeIcon sx={{ mr: 1, color: theme.palette.info.main }} />
                            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                              Ca {shiftIndex + 1}
                            </Typography>
                          </Box>
                          <Typography variant="body2" color="text.secondary">
                            {shift.startTime} - {shift.endTime}
                          </Typography>
                        </Box>
                        <Box sx={{ minWidth: '100px' }}>
                          <Typography variant="body2" color="text.secondary">Nhân công</Typography>
                          <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                            {shift.numberOfWorkers} người
                          </Typography>
                        </Box>
                        <Box sx={{ minWidth: '120px' }}>
                          <Typography variant="body2" color="text.secondary">Lương/ngày</Typography>
                          <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                            {formatCurrency(shift.salary || 0)}
                          </Typography>
                        </Box>
                        <Box sx={{ minWidth: '100px' }}>
                          <Typography variant="body2" color="text.secondary">Số ngày</Typography>
                          <Typography variant="body1" sx={{ fontWeight: 'medium', color: theme.palette.primary.main }}>
                            {workingDates.length} ngày
                          </Typography>
                        </Box>
                        <Box sx={{ minWidth: '150px' }}>
                          <Typography variant="body2" color="text.secondary">Tổng tiền ca</Typography>
                          <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
                            {formatCurrency(shiftAmount)}
                          </Typography>
                        </Box>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails sx={{ p: 3 }}>
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 2 }}>
                          Ngày làm việc trong tuần:
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
                          {formatWorkingDays(shift.workingDays).split(', ').map((day, index) => (
                            <Chip
                              key={index}
                              label={day}
                              color="primary"
                              variant="outlined"
                              size="small"
                            />
                          ))}
                        </Box>
                      </Box>

                      <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 2 }}>
                          Lịch làm việc chi tiết ({workingDates.length} ngày):
                        </Typography>
                        {Object.entries(datesByDayOfWeek)
                          .sort(([a], [b]) => parseInt(a) - parseInt(b))
                          .map(([dayOfWeek, dates]) => (
                            <Box key={dayOfWeek} sx={{ mb: 2 }}>
                              <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 1 }}>
                                {dayNames[parseInt(dayOfWeek)]} ({dates.length} ngày):
                              </Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                {dates.map((date, index) => (
                                  <Chip
                                    key={index}
                                    label={date}
                                    size="small"
                                    variant="filled"
                                    color="primary"
                                    sx={{ fontSize: '0.75rem' }}
                                  />
                                ))}
                              </Box>
                            </Box>
                          ))}
                      </Box>

                      <Paper sx={{ p: 2, backgroundColor: theme.palette.success.light }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                          Tính toán chi tiết:
                        </Typography>
                        <Typography variant="body2">
                          {formatCurrency(shift.salary || 0)} × {shift.numberOfWorkers} người × {workingDates.length} ngày = {formatCurrency(shiftAmount)}
                        </Typography>
                      </Paper>
                    </AccordionDetails>
                  </Accordion>
                );
              })}
            </CardContent>
          </Card>
        );
      })}
    </Box>
  );
};

export default ContractWorkSchedule;
