import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Box, Button, Typography, Chip } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import { ContractDetails } from '../components/contract';
import { CustomerContract, ContractStatusMap } from '../models';
import { contractService } from '../services/contract/contractService';
import { Loading<PERSON>pinner, ErrorAlert, PageHeader } from '../components/common';

const ContractDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [contract, setContract] = useState<CustomerContract | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchContract = async () => {
      if (!id) return;

      try {
        const data = await contractService.getContractById(parseInt(id, 10));
        setContract(data);
      } catch (err: any) {
        setError(err.message || 'Đã xảy ra lỗi khi tải thông tin hợp đồng');
      } finally {
        setLoading(false);
      }
    };

    fetchContract();
  }, [id]);

  const handleBack = () => {
    navigate('/contracts');
  };

  const handleEdit = () => {
    navigate(`/contracts/edit/${id}`);
  };

  if (loading) {
    return <LoadingSpinner fullScreen />;
  }

  if (error) {
    return <ErrorAlert message={error} />;
  }

  if (!contract) {
    return <ErrorAlert message="Không tìm thấy hợp đồng" />;
  }

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0: // Pending
        return 'warning';
      case 1: // Active
        return 'success';
      case 2: // Completed
        return 'info';
      case 3: // Cancelled
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Button startIcon={<ArrowBackIcon />} onClick={handleBack}>
          Quay lại danh sách hợp đồng
        </Button>
        <Button variant="contained" startIcon={<EditIcon />} onClick={handleEdit}>
          Chỉnh sửa hợp đồng
        </Button>
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <PageHeader
          title={`Hợp đồng #${contract.id}`}
          subtitle={`Khách hàng: ${contract.customerName}`}
        />
        <Chip
          label={ContractStatusMap[contract.status || 0]}
          color={getStatusColor(contract.status || 0)}
          sx={{ fontSize: '1rem', py: 1, px: 2 }}
        />
      </Box>

      <ContractDetails contract={contract} />
    </Box>
  );
};

export default ContractDetailsPage;
