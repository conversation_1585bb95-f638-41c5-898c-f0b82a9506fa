import React, { useState, useEffect } from 'react';
import { Alert, AlertTitle, Box, Collapse } from '@mui/material';

interface SuccessAlertProps {
  title?: string;
  message: string;
  autoHideDuration?: number;
}

const SuccessAlert: React.FC<SuccessAlertProps> = ({ 
  title = 'Success', 
  message, 
  autoHideDuration = 5000 
}) => {
  const [open, setOpen] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setOpen(false);
    }, autoHideDuration);

    return () => {
      clearTimeout(timer);
    };
  }, [autoHideDuration]);

  return (
    <Box sx={{ my: 2 }}>
      <Collapse in={open}>
        <Alert severity="success" onClose={() => setOpen(false)}>
          <AlertTitle>{title}</AlertTitle>
          {message}
        </Alert>
      </Collapse>
    </Box>
  );
};

export default SuccessAlert;
