spring.application.name=customer-payment-service
# PostgreSQL database configuration
spring.datasource.url=***************************************************
spring.datasource.username=postgres
spring.datasource.password=1234
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA configurations
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Server
server.port=8084

# Service URLs (using Docker service names)
customer.service.url=http://customer-service:8081/api/customer
customercontract.service.url=http://customer-contract-service:8083/api/customer-contract

# DevTools configuration
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
spring.devtools.restart.poll-interval=2s
spring.devtools.restart.quiet-period=1s
spring.devtools.remote.secret=mysecret

# Allow bean definition overriding
spring.main.allow-bean-definition-overriding=true
