import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Paper,
  Divider,
  <PERSON>per,
  <PERSON>,
  StepL<PERSON>l,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  useTheme,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import PersonIcon from '@mui/icons-material/Person';
import BusinessIcon from '@mui/icons-material/Business';
import DateRangeIcon from '@mui/icons-material/DateRange';
import DescriptionIcon from '@mui/icons-material/Description';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { CustomerContract, Customer, JobDetail } from '../../models';
import JobDetailForm from './JobDetailForm';
import ContractAmountCalculation from './ContractAmountCalculation';
import { PageHeader, DatePickerField } from '../common';
import { CustomerDialog } from '../customer';
import { calculateContractAmount, calculateContractDates } from '../../utils/contractCalculationUtils';

interface CustomerContractFormProps {
  contract: Partial<CustomerContract>;
  onChange: (contract: Partial<CustomerContract>) => void;
  onSubmit: () => void;
  isEdit?: boolean;
}

const CustomerContractForm: React.FC<CustomerContractFormProps> = ({
  contract,
  onChange,
  onSubmit,
  isEdit = false,
}) => {
  const [customerDialogOpen, setCustomerDialogOpen] = useState(false);
  const theme = useTheme();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const updatedContract = {
      ...contract,
      [name]: value,
    };

    // Auto-calculate total amount when dates change
    if (name === 'startingDate' || name === 'endingDate') {
      const calculation = calculateContractAmount(updatedContract);
      updatedContract.totalAmount = calculation.totalAmount;
    }

    onChange(updatedContract);
  };

  const handleOpenCustomerDialog = () => {
    setCustomerDialogOpen(true);
  };

  const handleCloseCustomerDialog = () => {
    setCustomerDialogOpen(false);
  };

  const handleSelectCustomer = (customer: Customer) => {
    onChange({
      ...contract,
      customerId: customer.id,
      customerName: customer.fullName,
    });
  };

  const handleJobDetailChange = (index: number, jobDetail: Partial<JobDetail>) => {
    const updatedJobDetails = [...(contract.jobDetails || [])];
    updatedJobDetails[index] = jobDetail as JobDetail;

    const updatedContract = {
      ...contract,
      jobDetails: updatedJobDetails,
    };

    // Auto-calculate contract dates from job details
    const contractDates = calculateContractDates(updatedContract);
    if (contractDates.startingDate && contractDates.endingDate) {
      updatedContract.startingDate = contractDates.startingDate;
      updatedContract.endingDate = contractDates.endingDate;
    }

    // Auto-calculate total amount when job details change
    const calculation = calculateContractAmount(updatedContract);
    updatedContract.totalAmount = calculation.totalAmount;

    onChange(updatedContract);
  };

  const handleAddJobDetail = () => {
    const newJobDetail: JobDetail = {
      jobCategoryId: 0,
      startDate: '',
      endDate: '',
      workLocation: '',
      workShifts: [],
    };

    onChange({
      ...contract,
      jobDetails: [...(contract.jobDetails || []), newJobDetail],
    });
  };

  const handleDeleteJobDetail = (index: number) => {
    const updatedJobDetails = [...(contract.jobDetails || [])];
    updatedJobDetails.splice(index, 1);

    onChange({
      ...contract,
      jobDetails: updatedJobDetails,
    });
  };

  return (
    <Box>
      <PageHeader
        title={isEdit ? "Chỉnh sửa Hợp đồng" : "Tạo Hợp đồng Mới"}
        subtitle="Nhập thông tin hợp đồng bên dưới"
      />

      {/* Contract workflow steps */}
      <Stepper
        activeStep={contract.customerId ? (contract.jobDetails?.length ? 2 : 1) : 0}
        alternativeLabel
        sx={{ mb: 4 }}
      >
        <Step>
          <StepLabel>Chọn khách hàng</StepLabel>
        </Step>
        <Step>
          <StepLabel>Thông tin hợp đồng</StepLabel>
        </Step>
        <Step>
          <StepLabel>Chi tiết công việc</StepLabel>
        </Step>
      </Stepper>

      {/* Contract header with customer selection */}
      <Paper
        elevation={3}
        sx={{
          p: 3,
          mb: 4,
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          background: theme.palette.background.paper,
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '8px',
            background: theme.palette.primary.main,
          }
        }}
      >
        <Typography variant="h5" sx={{ mb: 3, color: theme.palette.primary.main, fontWeight: 'bold' }}>
          HỢP ĐỒNG CUNG CẤP DỊCH VỤ NHÂN CÔNG
        </Typography>

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
          {/* Customer selection section */}
          <Box sx={{ width: { xs: '100%', md: '48%' } }}>
            <Card variant="outlined" sx={{ mb: 2, height: '100%' }}>
              <CardContent>
                <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                  <PersonIcon sx={{ mr: 1 }} />
                  Thông tin khách hàng
                </Typography>

                {contract.customerId ? (
                  <Box sx={{ p: 2, border: '1px dashed #ccc', borderRadius: '4px', mb: 2 }}>
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                      {contract.customerName}
                    </Typography>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={handleOpenCustomerDialog}
                      sx={{ mt: 1 }}
                    >
                      Thay đổi
                    </Button>
                  </Box>
                ) : (
                  <Button
                    variant="contained"
                    color="primary"
                    fullWidth
                    onClick={handleOpenCustomerDialog}
                    sx={{ height: 56 }}
                    startIcon={<PersonIcon />}
                  >
                    Chọn khách hàng
                  </Button>
                )}
              </CardContent>
            </Card>
          </Box>

          {/* Contract basic info */}
          <Box sx={{ width: { xs: '100%', md: '48%' } }}>
            <Card variant="outlined" sx={{ mb: 2, height: '100%' }}>
              <CardContent>
                <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                  <BusinessIcon sx={{ mr: 1 }} />
                  Địa điểm làm việc
                </Typography>
                <TextField
                  fullWidth
                  label="Địa chỉ làm việc"
                  name="address"
                  value={contract.address || ''}
                  onChange={handleInputChange}
                  required
                  placeholder="Nhập địa chỉ đầy đủ nơi thực hiện công việc"
                />
              </CardContent>
            </Card>
          </Box>

          {/* Contract dates */}
          <Box sx={{ width: { xs: '100%', md: '48%' } }}>
            <Card variant="outlined" sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                  <DateRangeIcon sx={{ mr: 1 }} />
                  Thời gian hiệu lực hợp đồng (Tự động tính toán)
                  <Tooltip title="Thời gian hợp đồng được tính tự động từ ngày bắt đầu sớm nhất và ngày kết thúc muộn nhất của các công việc">
                    <IconButton size="small" sx={{ ml: 1 }}>
                      <HelpOutlineIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                    <DatePickerField
                      label="Ngày bắt đầu (Tự động)"
                      value={contract.startingDate || ''}
                      onChange={() => {}} // Read-only
                      required
                      disabled
                    />
                  </Box>
                  <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                    <DatePickerField
                      label="Ngày kết thúc (Tự động)"
                      value={contract.endingDate || ''}
                      onChange={() => {}} // Read-only
                      required
                      disabled
                    />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Box>

          {/* Contract value - Auto calculated */}
          <Box sx={{ width: { xs: '100%', md: '48%' } }}>
            <Card variant="outlined" sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                  <MonetizationOnIcon sx={{ mr: 1 }} />
                  Giá trị hợp đồng (Tự động tính toán)
                  <Tooltip title="Tổng giá trị hợp đồng được tính tự động dựa trên lương, số người và số ngày làm việc">
                    <IconButton size="small" sx={{ ml: 1 }}>
                      <HelpOutlineIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Typography>
                <TextField
                  fullWidth
                  label="Tổng giá trị hợp đồng (VNĐ)"
                  name="totalAmount"
                  type="text"
                  value={contract.totalAmount ? contract.totalAmount.toLocaleString('vi-VN') + ' VNĐ' : '0 VNĐ'}
                  slotProps={{
                    input: {
                      readOnly: true,
                    },
                  }}
                  sx={{
                    '& input': {
                      fontWeight: 'bold',
                      color: theme.palette.success.main,
                      backgroundColor: theme.palette.action.hover
                    }
                  }}
                />
              </CardContent>
            </Card>
          </Box>

          {/* Description */}
          <Box sx={{ width: '100%' }}>
            <Card variant="outlined" sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                  <DescriptionIcon sx={{ mr: 1 }} />
                  Mô tả hợp đồng
                </Typography>
                <TextField
                  fullWidth
                  label="Mô tả chi tiết về hợp đồng"
                  name="description"
                  value={contract.description || ''}
                  onChange={handleInputChange}
                  multiline
                  rows={3}
                  placeholder="Nhập các thông tin bổ sung về hợp đồng (không bắt buộc)"
                />
              </CardContent>
            </Card>
          </Box>
        </Box>

        <CustomerDialog
          open={customerDialogOpen}
          onClose={handleCloseCustomerDialog}
          onSelectCustomer={handleSelectCustomer}
        />
      </Paper>

      {/* Job details section */}
      <Typography variant="h5" sx={{ mb: 2, mt: 4, fontWeight: 'bold', color: theme.palette.primary.main }}>
        CHI TIẾT CÔNG VIỆC
      </Typography>

      {(contract.jobDetails || []).map((jobDetail, index) => (
        <JobDetailForm
          key={index}
          jobDetail={jobDetail}
          onChange={(updatedJobDetail) => handleJobDetailChange(index, updatedJobDetail)}
          onDelete={() => handleDeleteJobDetail(index)}
          showDelete={true}
        />
      ))}

      <Button
        variant="outlined"
        startIcon={<AddIcon />}
        onClick={handleAddJobDetail}
        sx={{ mb: 3 }}
      >
        Thêm công việc
      </Button>

      {/* Contract Amount Calculation */}
      <ContractAmountCalculation contract={contract} />

      <Divider sx={{ my: 3 }} />

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
        <Button
          variant="contained"
          color="primary"
          size="large"
          onClick={onSubmit}
          disabled={!contract.customerId || !contract.address || !(contract.jobDetails && contract.jobDetails.length > 0)}
        >
          {isEdit ? 'Cập nhật Hợp đồng' : 'Tạo Hợp đồng'}
        </Button>
      </Box>
    </Box>
  );
};

export default CustomerContractForm;
