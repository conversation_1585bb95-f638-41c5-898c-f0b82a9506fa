import { CustomerContract, JobDetail, WorkShift, Customer } from '../../models';
import { get, post, put, del } from '../api/apiClient';

interface JobCategory {
  id: number;
  name: string;
  description?: string;
  isDeleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

const CONTRACT_URL = '/api/customer-contract';
const JOB_DETAIL_URL = '/api/job-detail';
const WORK_SHIFT_URL = '/api/work-shift';

export const contractService = {
  // Contract operations
  getAllContracts: async (): Promise<CustomerContract[]> => {
    const contracts = await get<CustomerContract[]>(CONTRACT_URL);

    // Ensure each contract has customer name
    for (const contract of contracts) {
      if (!contract.customerName && contract.customerId) {
        try {
          const customerResponse = await get<Customer>(`/api/customer/${contract.customerId}`);
          if (customerResponse && customerResponse.fullName) {
            contract.customerName = customerResponse.fullName;
          }
        } catch (error) {
          console.error(`Error fetching customer details for contract #${contract.id}:`, error);
        }
      }
    }

    return contracts;
  },

  getContractById: async (id: number): Promise<CustomerContract> => {
    const contract = await get<CustomerContract>(`${CONTRACT_URL}/${id}`);

    // Nếu không có tên khách hàng, thử lấy thông tin khách hàng
    if (!contract.customerName && contract.customerId) {
      try {
        const customerResponse = await get<Customer>(`/api/customer/${contract.customerId}`);
        if (customerResponse && customerResponse.fullName) {
          contract.customerName = customerResponse.fullName;
        }
      } catch (error) {
        console.error('Error fetching customer details:', error);
      }
    }

    // Đảm bảo mỗi job detail có tên loại công việc
    if (contract.jobDetails && contract.jobDetails.length > 0) {
      for (const jobDetail of contract.jobDetails) {
        if (!jobDetail.jobCategoryName && jobDetail.jobCategoryId) {
          try {
            const jobCategoryResponse = await get<JobCategory>(`/api/job-category/${jobDetail.jobCategoryId}`);
            if (jobCategoryResponse && jobCategoryResponse.name) {
              jobDetail.jobCategoryName = jobCategoryResponse.name;
            }
          } catch (error) {
            console.error('Error fetching job category details:', error);
          }
        }
      }
    }

    return contract;
  },

  getContractsByCustomerId: async (customerId: number): Promise<CustomerContract[]> => {
    return get<CustomerContract[]>(`${CONTRACT_URL}/customer/${customerId}`);
  },

  createContract: async (contract: CustomerContract): Promise<CustomerContract> => {
    return post<CustomerContract>(CONTRACT_URL, contract);
  },

  updateContract: async (contract: CustomerContract): Promise<CustomerContract> => {
    return put<CustomerContract>(CONTRACT_URL, contract);
  },

  updateContractStatus: async (id: number, status: number): Promise<CustomerContract> => {
    return put<CustomerContract>(`${CONTRACT_URL}/${id}/status?status=${status}`);
  },

  signContract: async (id: number, signedDate: string): Promise<CustomerContract> => {
    return put<CustomerContract>(`${CONTRACT_URL}/${id}/sign?signedDate=${signedDate}`);
  },

  deleteContract: async (id: number): Promise<void> => {
    return del<void>(`${CONTRACT_URL}/${id}`);
  },

  // Job Detail operations
  getJobDetailById: async (id: number): Promise<JobDetail> => {
    return get<JobDetail>(`${JOB_DETAIL_URL}/${id}`);
  },

  createJobDetail: async (jobDetail: JobDetail): Promise<JobDetail> => {
    return post<JobDetail>(JOB_DETAIL_URL, jobDetail);
  },

  updateJobDetail: async (jobDetail: JobDetail): Promise<JobDetail> => {
    return put<JobDetail>(JOB_DETAIL_URL, jobDetail);
  },

  deleteJobDetail: async (id: number): Promise<void> => {
    return del<void>(`${JOB_DETAIL_URL}/${id}`);
  },

  // Work Shift operations
  getWorkShiftById: async (id: number): Promise<WorkShift> => {
    return get<WorkShift>(`${WORK_SHIFT_URL}/${id}`);
  },

  createWorkShift: async (workShift: WorkShift): Promise<WorkShift> => {
    return post<WorkShift>(WORK_SHIFT_URL, workShift);
  },

  updateWorkShift: async (workShift: WorkShift): Promise<WorkShift> => {
    return put<WorkShift>(WORK_SHIFT_URL, workShift);
  },

  deleteWorkShift: async (id: number): Promise<void> => {
    return del<void>(`${WORK_SHIFT_URL}/${id}`);
  }
};
