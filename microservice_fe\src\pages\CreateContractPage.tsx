import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box } from '@mui/material';
import { CustomerContractForm } from '../components/contract';
import { CustomerContract } from '../models';
import { contractService } from '../services/contract/contractService';
import { LoadingSpinner, ErrorAlert, SuccessAlert } from '../components/common';
import { calculateContractAmount } from '../utils/contractCalculationUtils';

const CreateContractPage: React.FC = () => {
  const navigate = useNavigate();
  const [contract, setContract] = useState<Partial<CustomerContract>>({
    customerId: 0,
    startingDate: '',
    endingDate: '',
    totalAmount: 0,
    address: '',
    description: '',
    jobDetails: [],
    status: 0 // Pending
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleContractChange = (updatedContract: Partial<CustomerContract>) => {
    setContract(updatedContract);
  };

  const validateContract = (): boolean => {
    if (!contract.customerId || contract.customerId === 0) {
      setError('Vui lòng chọn khách hàng');
      return false;
    }

    // Contract dates are auto-calculated from job details, no need to validate manually

    if (!contract.address) {
      setError('Vui lòng nhập địa chỉ hợp đồng');
      return false;
    }

    // Total amount is auto-calculated, no need to validate manually

    if (!contract.jobDetails || contract.jobDetails.length === 0) {
      setError('Vui lòng thêm ít nhất một chi tiết công việc');
      return false;
    }

    // Validate each job detail
    for (const jobDetail of contract.jobDetails) {
      if (!jobDetail.jobCategoryId || jobDetail.jobCategoryId === 0) {
        setError('Vui lòng chọn loại công việc cho tất cả chi tiết công việc');
        return false;
      }

      if (!jobDetail.startDate) {
        setError('Vui lòng nhập ngày bắt đầu cho tất cả chi tiết công việc');
        return false;
      }

      if (!jobDetail.endDate) {
        setError('Vui lòng nhập ngày kết thúc cho tất cả chi tiết công việc');
        return false;
      }

      if (!jobDetail.workLocation) {
        setError('Vui lòng nhập địa điểm làm việc cho tất cả chi tiết công việc');
        return false;
      }

      if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) {
        setError('Vui lòng thêm ít nhất một ca làm việc cho mỗi chi tiết công việc');
        return false;
      }

      // Validate each work shift
      for (const workShift of jobDetail.workShifts) {
        if (!workShift.startTime) {
          setError('Vui lòng nhập giờ bắt đầu cho tất cả ca làm việc');
          return false;
        }

        if (!workShift.endTime) {
          setError('Vui lòng nhập giờ kết thúc cho tất cả ca làm việc');
          return false;
        }

        if (!workShift.numberOfWorkers || workShift.numberOfWorkers <= 0) {
          setError('Vui lòng nhập số lượng người lao động hợp lệ cho tất cả ca làm việc');
          return false;
        }

        if (workShift.salary === undefined || workShift.salary < 0) {
          setError('Vui lòng nhập mức lương hợp lệ cho tất cả ca làm việc');
          return false;
        }

        if (!workShift.workingDays) {
          setError('Vui lòng chọn ngày làm việc cho tất cả ca làm việc');
          return false;
        }
      }
    }

    return true;
  };

  const handleSubmit = async () => {
    setError(null);

    if (!validateContract()) {
      return;
    }

    // Ensure total amount is calculated before submitting
    const calculation = calculateContractAmount(contract);
    const contractToSubmit = {
      ...contract,
      totalAmount: calculation.totalAmount
    };

    setLoading(true);

    try {
      const createdContract = await contractService.createContract(contractToSubmit as CustomerContract);
      setSuccess('Hợp đồng đã được tạo thành công!');

      // Redirect to the contract details page after a short delay
      setTimeout(() => {
        navigate(`/contracts/${createdContract.id}`);
      }, 2000);
    } catch (err: any) {
      setError(err.message || 'Đã xảy ra lỗi khi tạo hợp đồng');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingSpinner fullScreen />;
  }

  return (
    <Box>
      {error && <ErrorAlert message={error} />}
      {success && <SuccessAlert message={success} />}

      <CustomerContractForm
        contract={contract}
        onChange={handleContractChange}
        onSubmit={handleSubmit}
      />
    </Box>
  );
};

export default CreateContractPage;
