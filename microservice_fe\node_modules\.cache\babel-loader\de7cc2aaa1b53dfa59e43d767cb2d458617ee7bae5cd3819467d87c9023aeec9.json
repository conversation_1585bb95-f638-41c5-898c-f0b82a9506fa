{"ast": null, "code": "import { normalizeCustomerRevenue, normalizeTimeBasedRevenue } from '../../models';\nimport { get } from '../api/apiClient';\nimport axios from 'axios';\nimport { formatDateForInput } from '../../utils/dateUtils';\n\n// Đảm bảo URL đúng, thêm kiểm tra môi trường\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';\nconst BASE_URL = '/api/customer-statistics';\n\n// Hàm kiểm tra kết nối đến API\nconst checkApiConnection = async () => {\n  try {\n    console.log(`Checking API connection to: ${API_BASE_URL}${BASE_URL}/health`);\n\n    // Thử kết nối trực tiếp đến API Gateway\n    const response = await axios.get(`${API_BASE_URL}${BASE_URL}/health`, {\n      timeout: 5000,\n      headers: {\n        'Accept': 'application/json',\n        'Content-Type': 'application/json',\n        'Cache-Control': 'no-cache'\n      }\n    });\n    console.log('API health check response:', response.status, response.data);\n    return response.status === 200;\n  } catch (error) {\n    console.error('API connection check failed:', error);\n\n    // Thử kết nối trực tiếp đến service\n    try {\n      console.log('Trying direct connection to service at http://localhost:8085/api/customer-statistics/health');\n      const directResponse = await axios.get('http://localhost:8085/api/customer-statistics/health', {\n        timeout: 5000,\n        headers: {\n          'Accept': 'application/json',\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('Direct API health check response:', directResponse.status, directResponse.data);\n      return directResponse.status === 200;\n    } catch (directError) {\n      console.error('Direct API connection check failed:', directError);\n      return false;\n    }\n  }\n};\nexport const customerStatisticsService = {\n  // Get customer revenue statistics\n  getCustomerRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8089/api/customer-statistics/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(customer => {\n            // Đảm bảo customer không null\n            if (!customer) return normalizeCustomerRevenue(null);\n            return normalizeCustomerRevenue(customer);\n          }).filter(customer => customer !== null);\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping customer revenue data:', err);\n          return [];\n        }\n      }\n\n      // If result is not an array, try to convert it\n      if (typeof result === 'object') {\n        console.warn('Result is not an array, attempting to convert:', result);\n        try {\n          // Nếu là object, thử chuyển thành array\n          const singleItem = normalizeCustomerRevenue(result);\n          return [singleItem];\n        } catch (err) {\n          console.error('Failed to convert object to array:', err);\n        }\n      }\n      console.error('Invalid result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerRevenueStatistics:', error);\n      if (axios.isAxiosError(error)) {\n        var _error$response, _error$response2, _error$config;\n        console.error('Response data:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n        console.error('Response status:', (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status);\n        console.error('Request URL:', (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url);\n\n        // Kiểm tra lỗi CORS\n        if (error.message.includes('Network Error') || !error.response) {\n          console.error('Possible CORS or network issue');\n        }\n      }\n      throw error;\n    }\n  },\n  // Get customer invoices\n  getCustomerInvoices: async (customerId, startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        customerId,\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        result = await get(`${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get invoices through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8089/api/customer-statistics/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('Invoices result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result for invoices');\n        return [];\n      }\n\n      // Ensure we have a valid array\n      if (Array.isArray(result)) {\n        // Xử lý dữ liệu trả về để đảm bảo các trường cần thiết\n        return result.map(invoice => ({\n          ...invoice,\n          // Đảm bảo các trường quan trọng có giá trị mặc định nếu null\n          id: invoice.id || 0,\n          paymentCode: invoice.paymentCode || '',\n          paymentAmount: typeof invoice.paymentAmount === 'number' ? invoice.paymentAmount : 0,\n          contractCode: invoice.contractCode || 'Không xác định',\n          paymentDate: invoice.paymentDate ? new Date(invoice.paymentDate) : new Date()\n        })).filter(invoice => invoice !== null);\n      }\n\n      // If result is not an array but is an object, try to convert it\n      if (typeof result === 'object' && result !== null) {\n        console.warn('Invoices result is not an array, attempting to convert:', result);\n        try {\n          // Nếu là object, thử chuyển thành array\n          return [result];\n        } catch (err) {\n          console.error('Failed to convert invoice object to array:', err);\n        }\n      }\n\n      // If result is not an array, return empty array\n      console.error('Invalid invoices result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerInvoices:', error);\n      if (axios.isAxiosError(error)) {\n        var _error$response3, _error$response4, _error$config2;\n        console.error('Response data:', (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data);\n        console.error('Response status:', (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status);\n        console.error('Request URL:', (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.url);\n\n        // Kiểm tra lỗi CORS\n        if (error.message.includes('Network Error') || !error.response) {\n          console.error('Possible CORS or network issue in getCustomerInvoices');\n        }\n      }\n      throw error;\n    }\n  },\n  // Get daily revenue statistics\n  getDailyRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8089/api/customer-statistics/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping daily revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching daily revenue statistics:', error);\n      throw error;\n    }\n  },\n  // Get weekly revenue statistics\n  getWeeklyRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8089/api/customer-statistics/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null);\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping weekly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching weekly revenue statistics:', error);\n      throw error;\n    }\n  },\n  // Get monthly revenue statistics\n  getMonthlyRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8089/api/customer-statistics/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping monthly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching monthly revenue statistics:', error);\n      throw error;\n    }\n  },\n  // Get yearly revenue statistics\n  getYearlyRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8089/api/customer-statistics/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null)\n          // Sắp xếp theo năm tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping yearly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching yearly revenue statistics:', error);\n      throw error;\n    }\n  }\n};\n\n// Helper function to format date to string in ISO format (yyyy-MM-dd)\nconst formatDateToString = date => {\n  try {\n    // Ensure we have a valid date\n    if (!(date instanceof Date) || isNaN(date.getTime())) {\n      console.error('Invalid date provided:', date);\n      // Return today's date as fallback\n      const today = new Date();\n      return formatDateForInput(today);\n    }\n\n    // Xử lý đặc biệt để tránh vấn đề múi giờ\n    // Đặt giờ là 12:00:00 để tránh vấn đề chuyển đổi múi giờ\n    const adjustedDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 12, 0, 0);\n\n    // Format to ISO date string using our utility function\n    return formatDateForInput(adjustedDate);\n  } catch (error) {\n    console.error('Error formatting date:', error);\n    // Return today's date as fallback\n    const today = new Date();\n    return formatDateForInput(today);\n  }\n};", "map": {"version": 3, "names": ["normalizeCustomerRevenue", "normalizeTimeBasedRevenue", "get", "axios", "formatDateForInput", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "BASE_URL", "checkApiConnection", "console", "log", "response", "timeout", "headers", "status", "data", "error", "directResponse", "directError", "customerStatisticsService", "getCustomerRevenueStatistics", "startDate", "endDate", "isConnected", "Error", "formattedStartDate", "formatDateToString", "formattedEndDate", "result", "gatewayError", "Array", "isArray", "normalizedData", "map", "customer", "filter", "err", "warn", "singleItem", "isAxiosError", "_error$response", "_error$response2", "_error$config", "config", "url", "message", "includes", "getCustomerInvoices", "customerId", "invoice", "id", "paymentCode", "paymentAmount", "contractCode", "paymentDate", "Date", "_error$response3", "_error$response4", "_error$config2", "getDailyRevenueStatistics", "item", "sort", "a", "b", "dateA", "date", "dateB", "getTime", "getWeeklyRevenueStatistics", "getMonthlyRevenueStatistics", "getYearlyRevenueStatistics", "isNaN", "today", "adjustedDate", "getFullYear", "getMonth", "getDate"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/services/statistics/customerStatisticsService.ts"], "sourcesContent": ["import { CustomerRevenue, CustomerPayment, TimeBasedRevenue, normalizeCustomerRevenue, normalizeTimeBasedRevenue } from '../../models';\nimport { get } from '../api/apiClient';\nimport axios from 'axios';\nimport { formatDateForInput } from '../../utils/dateUtils';\n\n// Đảm bảo URL đúng, thêm kiểm tra môi trường\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';\nconst BASE_URL = '/api/customer-statistics';\n\n// Hàm kiểm tra kết nối đến API\nconst checkApiConnection = async (): Promise<boolean> => {\n  try {\n    console.log(`Checking API connection to: ${API_BASE_URL}${BASE_URL}/health`);\n\n    // Thử kết nối trực tiếp đến API Gateway\n    const response = await axios.get(`${API_BASE_URL}${BASE_URL}/health`, {\n      timeout: 5000,\n      headers: {\n        'Accept': 'application/json',\n        'Content-Type': 'application/json',\n        'Cache-Control': 'no-cache'\n      }\n    });\n\n    console.log('API health check response:', response.status, response.data);\n    return response.status === 200;\n  } catch (error) {\n    console.error('API connection check failed:', error);\n\n    // Thử kết nối trực tiếp đến service\n    try {\n      console.log('Trying direct connection to service at http://localhost:8085/api/customer-statistics/health');\n      const directResponse = await axios.get('http://localhost:8085/api/customer-statistics/health', {\n        timeout: 5000,\n        headers: {\n          'Accept': 'application/json',\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('Direct API health check response:', directResponse.status, directResponse.data);\n      return directResponse.status === 200;\n    } catch (directError) {\n      console.error('Direct API connection check failed:', directError);\n      return false;\n    }\n  }\n};\n\nexport const customerStatisticsService = {\n  // Get customer revenue statistics\n  getCustomerRevenueStatistics: async (startDate: Date, endDate: Date): Promise<CustomerRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<CustomerRevenue[]>(\n          `${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8089/api/customer-statistics/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(customer => {\n            // Đảm bảo customer không null\n            if (!customer) return normalizeCustomerRevenue(null);\n            return normalizeCustomerRevenue(customer);\n          }).filter(customer => customer !== null);\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping customer revenue data:', err);\n          return [];\n        }\n      }\n\n      // If result is not an array, try to convert it\n      if (typeof result === 'object') {\n        console.warn('Result is not an array, attempting to convert:', result);\n        try {\n          // Nếu là object, thử chuyển thành array\n          const singleItem = normalizeCustomerRevenue(result);\n          return [singleItem];\n        } catch (err) {\n          console.error('Failed to convert object to array:', err);\n        }\n      }\n\n      console.error('Invalid result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerRevenueStatistics:', error);\n      if (axios.isAxiosError(error)) {\n        console.error('Response data:', error.response?.data);\n        console.error('Response status:', error.response?.status);\n        console.error('Request URL:', error.config?.url);\n\n        // Kiểm tra lỗi CORS\n        if (error.message.includes('Network Error') || !error.response) {\n          console.error('Possible CORS or network issue');\n        }\n      }\n      throw error;\n    }\n  },\n\n  // Get customer invoices\n  getCustomerInvoices: async (customerId: number, startDate: Date, endDate: Date): Promise<CustomerPayment[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { customerId, startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        result = await get<CustomerPayment[]>(\n          `${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get invoices through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8089/api/customer-statistics/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('Invoices result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result for invoices');\n        return [];\n      }\n\n      // Ensure we have a valid array\n      if (Array.isArray(result)) {\n        // Xử lý dữ liệu trả về để đảm bảo các trường cần thiết\n        return result.map(invoice => ({\n          ...invoice,\n          // Đảm bảo các trường quan trọng có giá trị mặc định nếu null\n          id: invoice.id || 0,\n          paymentCode: invoice.paymentCode || '',\n          paymentAmount: typeof invoice.paymentAmount === 'number' ? invoice.paymentAmount : 0,\n          contractCode: invoice.contractCode || 'Không xác định',\n          paymentDate: invoice.paymentDate ? new Date(invoice.paymentDate) : new Date()\n        })).filter(invoice => invoice !== null);\n      }\n\n      // If result is not an array but is an object, try to convert it\n      if (typeof result === 'object' && result !== null) {\n        console.warn('Invoices result is not an array, attempting to convert:', result);\n        try {\n          // Nếu là object, thử chuyển thành array\n          return [result as CustomerPayment];\n        } catch (err) {\n          console.error('Failed to convert invoice object to array:', err);\n        }\n      }\n\n      // If result is not an array, return empty array\n      console.error('Invalid invoices result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerInvoices:', error);\n      if (axios.isAxiosError(error)) {\n        console.error('Response data:', error.response?.data);\n        console.error('Response status:', error.response?.status);\n        console.error('Request URL:', error.config?.url);\n\n        // Kiểm tra lỗi CORS\n        if (error.message.includes('Network Error') || !error.response) {\n          console.error('Possible CORS or network issue in getCustomerInvoices');\n        }\n      }\n      throw error;\n    }\n  },\n\n  // Get daily revenue statistics\n  getDailyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8089/api/customer-statistics/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          })\n          .filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping daily revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching daily revenue statistics:', error);\n      throw error;\n    }\n  },\n\n  // Get weekly revenue statistics\n  getWeeklyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8089/api/customer-statistics/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null);\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping weekly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching weekly revenue statistics:', error);\n      throw error;\n    }\n  },\n\n  // Get monthly revenue statistics\n  getMonthlyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8089/api/customer-statistics/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          })\n          .filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping monthly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching monthly revenue statistics:', error);\n      throw error;\n    }\n  },\n\n  // Get yearly revenue statistics\n  getYearlyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8089/api/customer-statistics/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          })\n          .filter(item => item !== null)\n          // Sắp xếp theo năm tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping yearly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching yearly revenue statistics:', error);\n      throw error;\n    }\n  }\n};\n\n// Helper function to format date to string in ISO format (yyyy-MM-dd)\nconst formatDateToString = (date: Date): string => {\n  try {\n    // Ensure we have a valid date\n    if (!(date instanceof Date) || isNaN(date.getTime())) {\n      console.error('Invalid date provided:', date);\n      // Return today's date as fallback\n      const today = new Date();\n      return formatDateForInput(today);\n    }\n\n    // Xử lý đặc biệt để tránh vấn đề múi giờ\n    // Đặt giờ là 12:00:00 để tránh vấn đề chuyển đổi múi giờ\n    const adjustedDate = new Date(\n      date.getFullYear(),\n      date.getMonth(),\n      date.getDate(),\n      12, 0, 0\n    );\n\n    // Format to ISO date string using our utility function\n    return formatDateForInput(adjustedDate);\n  } catch (error) {\n    console.error('Error formatting date:', error);\n    // Return today's date as fallback\n    const today = new Date();\n    return formatDateForInput(today);\n  }\n};\n"], "mappings": "AAAA,SAA6DA,wBAAwB,EAAEC,yBAAyB,QAAQ,cAAc;AACtI,SAASC,GAAG,QAAQ,kBAAkB;AACtC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,kBAAkB,QAAQ,uBAAuB;;AAE1D;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAC7E,MAAMC,QAAQ,GAAG,0BAA0B;;AAE3C;AACA,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAA8B;EACvD,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,+BAA+BP,YAAY,GAAGI,QAAQ,SAAS,CAAC;;IAE5E;IACA,MAAMI,QAAQ,GAAG,MAAMV,KAAK,CAACD,GAAG,CAAC,GAAGG,YAAY,GAAGI,QAAQ,SAAS,EAAE;MACpEK,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;QACP,QAAQ,EAAE,kBAAkB;QAC5B,cAAc,EAAE,kBAAkB;QAClC,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;IAEFJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,QAAQ,CAACG,MAAM,EAAEH,QAAQ,CAACI,IAAI,CAAC;IACzE,OAAOJ,QAAQ,CAACG,MAAM,KAAK,GAAG;EAChC,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdP,OAAO,CAACO,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;;IAEpD;IACA,IAAI;MACFP,OAAO,CAACC,GAAG,CAAC,6FAA6F,CAAC;MAC1G,MAAMO,cAAc,GAAG,MAAMhB,KAAK,CAACD,GAAG,CAAC,sDAAsD,EAAE;QAC7FY,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;UACP,QAAQ,EAAE,kBAAkB;UAC5B,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACFJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEO,cAAc,CAACH,MAAM,EAAEG,cAAc,CAACF,IAAI,CAAC;MAC5F,OAAOE,cAAc,CAACH,MAAM,KAAK,GAAG;IACtC,CAAC,CAAC,OAAOI,WAAW,EAAE;MACpBT,OAAO,CAACO,KAAK,CAAC,qCAAqC,EAAEE,WAAW,CAAC;MACjE,OAAO,KAAK;IACd;EACF;AACF,CAAC;AAED,OAAO,MAAMC,yBAAyB,GAAG;EACvC;EACAC,4BAA4B,EAAE,MAAAA,CAAOC,SAAe,EAAEC,OAAa,KAAiC;IAClG,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,MAAMf,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACe,WAAW,EAAE;QAChBd,OAAO,CAACO,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIQ,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACL,SAAS,CAAC;MACxD,MAAMM,gBAAgB,GAAGD,kBAAkB,CAACJ,OAAO,CAAC;;MAEpD;MACAb,OAAO,CAACC,GAAG,CAAC,gBAAgBH,QAAQ,sBAAsBkB,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MAC3GlB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QAAEW,SAAS,EAAEI,kBAAkB;QAAEH,OAAO,EAAEK;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIC,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAM5B,GAAG,CAChB,GAAGO,QAAQ,sBAAsBkB,kBAAkB,YAAYE,gBAAgB,EAAE,EACjF;UACEf,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOgB,YAAY,EAAE;QACrBpB,OAAO,CAACO,KAAK,CAAC,yCAAyC,EAAEa,YAAY,CAAC;QACtEpB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMO,cAAc,GAAG,MAAMhB,KAAK,CAACD,GAAG,CACpC,mEAAmEyB,kBAAkB,YAAYE,gBAAgB,EAAE,EACnH;UACEf,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDe,MAAM,GAAGX,cAAc,CAACF,IAAI;MAC9B;MAEAN,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEkB,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXnB,OAAO,CAACO,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIc,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMI,cAAc,GAAGJ,MAAM,CAACK,GAAG,CAACC,QAAQ,IAAI;YAC5C;YACA,IAAI,CAACA,QAAQ,EAAE,OAAOpC,wBAAwB,CAAC,IAAI,CAAC;YACpD,OAAOA,wBAAwB,CAACoC,QAAQ,CAAC;UAC3C,CAAC,CAAC,CAACC,MAAM,CAACD,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAAC;UAExCzB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEsB,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZ3B,OAAO,CAACO,KAAK,CAAC,sCAAsC,EAAEoB,GAAG,CAAC;UAC1D,OAAO,EAAE;QACX;MACF;;MAEA;MACA,IAAI,OAAOR,MAAM,KAAK,QAAQ,EAAE;QAC9BnB,OAAO,CAAC4B,IAAI,CAAC,gDAAgD,EAAET,MAAM,CAAC;QACtE,IAAI;UACF;UACA,MAAMU,UAAU,GAAGxC,wBAAwB,CAAC8B,MAAM,CAAC;UACnD,OAAO,CAACU,UAAU,CAAC;QACrB,CAAC,CAAC,OAAOF,GAAG,EAAE;UACZ3B,OAAO,CAACO,KAAK,CAAC,oCAAoC,EAAEoB,GAAG,CAAC;QAC1D;MACF;MAEA3B,OAAO,CAACO,KAAK,CAAC,wBAAwB,EAAEY,MAAM,CAAC;MAC/C,OAAO,EAAE;IACX,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,IAAIf,KAAK,CAACsC,YAAY,CAACvB,KAAK,CAAC,EAAE;QAAA,IAAAwB,eAAA,EAAAC,gBAAA,EAAAC,aAAA;QAC7BjC,OAAO,CAACO,KAAK,CAAC,gBAAgB,GAAAwB,eAAA,GAAExB,KAAK,CAACL,QAAQ,cAAA6B,eAAA,uBAAdA,eAAA,CAAgBzB,IAAI,CAAC;QACrDN,OAAO,CAACO,KAAK,CAAC,kBAAkB,GAAAyB,gBAAA,GAAEzB,KAAK,CAACL,QAAQ,cAAA8B,gBAAA,uBAAdA,gBAAA,CAAgB3B,MAAM,CAAC;QACzDL,OAAO,CAACO,KAAK,CAAC,cAAc,GAAA0B,aAAA,GAAE1B,KAAK,CAAC2B,MAAM,cAAAD,aAAA,uBAAZA,aAAA,CAAcE,GAAG,CAAC;;QAEhD;QACA,IAAI5B,KAAK,CAAC6B,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC9B,KAAK,CAACL,QAAQ,EAAE;UAC9DF,OAAO,CAACO,KAAK,CAAC,gCAAgC,CAAC;QACjD;MACF;MACA,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA+B,mBAAmB,EAAE,MAAAA,CAAOC,UAAkB,EAAE3B,SAAe,EAAEC,OAAa,KAAiC;IAC7G,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,MAAMf,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACe,WAAW,EAAE;QAChBd,OAAO,CAACO,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIQ,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACL,SAAS,CAAC;MACxD,MAAMM,gBAAgB,GAAGD,kBAAkB,CAACJ,OAAO,CAAC;;MAEpD;MACAb,OAAO,CAACC,GAAG,CAAC,gBAAgBH,QAAQ,aAAayC,UAAU,uBAAuBvB,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MACnIlB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QAAEsC,UAAU;QAAE3B,SAAS,EAAEI,kBAAkB;QAAEH,OAAO,EAAEK;MAAiB,CAAC,CAAC;;MAE5G;MACA,IAAIC,MAAM;MACV,IAAI;QACFA,MAAM,GAAG,MAAM5B,GAAG,CAChB,GAAGO,QAAQ,aAAayC,UAAU,uBAAuBvB,kBAAkB,YAAYE,gBAAgB,EAAE,EACzG;UACEf,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOgB,YAAY,EAAE;QACrBpB,OAAO,CAACO,KAAK,CAAC,6CAA6C,EAAEa,YAAY,CAAC;QAC1EpB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMO,cAAc,GAAG,MAAMhB,KAAK,CAACD,GAAG,CACpC,0DAA0DgD,UAAU,uBAAuBvB,kBAAkB,YAAYE,gBAAgB,EAAE,EAC3I;UACEf,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDe,MAAM,GAAGX,cAAc,CAACF,IAAI;MAC9B;MAEAN,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkB,MAAM,CAAC;;MAEvC;MACA,IAAI,CAACA,MAAM,EAAE;QACXnB,OAAO,CAACO,KAAK,CAAC,oDAAoD,CAAC;QACnE,OAAO,EAAE;MACX;;MAEA;MACA,IAAIc,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;QACzB;QACA,OAAOA,MAAM,CAACK,GAAG,CAACgB,OAAO,KAAK;UAC5B,GAAGA,OAAO;UACV;UACAC,EAAE,EAAED,OAAO,CAACC,EAAE,IAAI,CAAC;UACnBC,WAAW,EAAEF,OAAO,CAACE,WAAW,IAAI,EAAE;UACtCC,aAAa,EAAE,OAAOH,OAAO,CAACG,aAAa,KAAK,QAAQ,GAAGH,OAAO,CAACG,aAAa,GAAG,CAAC;UACpFC,YAAY,EAAEJ,OAAO,CAACI,YAAY,IAAI,gBAAgB;UACtDC,WAAW,EAAEL,OAAO,CAACK,WAAW,GAAG,IAAIC,IAAI,CAACN,OAAO,CAACK,WAAW,CAAC,GAAG,IAAIC,IAAI,CAAC;QAC9E,CAAC,CAAC,CAAC,CAACpB,MAAM,CAACc,OAAO,IAAIA,OAAO,KAAK,IAAI,CAAC;MACzC;;MAEA;MACA,IAAI,OAAOrB,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;QACjDnB,OAAO,CAAC4B,IAAI,CAAC,yDAAyD,EAAET,MAAM,CAAC;QAC/E,IAAI;UACF;UACA,OAAO,CAACA,MAAM,CAAoB;QACpC,CAAC,CAAC,OAAOQ,GAAG,EAAE;UACZ3B,OAAO,CAACO,KAAK,CAAC,4CAA4C,EAAEoB,GAAG,CAAC;QAClE;MACF;;MAEA;MACA3B,OAAO,CAACO,KAAK,CAAC,iCAAiC,EAAEY,MAAM,CAAC;MACxD,OAAO,EAAE;IACX,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,IAAIf,KAAK,CAACsC,YAAY,CAACvB,KAAK,CAAC,EAAE;QAAA,IAAAwC,gBAAA,EAAAC,gBAAA,EAAAC,cAAA;QAC7BjD,OAAO,CAACO,KAAK,CAAC,gBAAgB,GAAAwC,gBAAA,GAAExC,KAAK,CAACL,QAAQ,cAAA6C,gBAAA,uBAAdA,gBAAA,CAAgBzC,IAAI,CAAC;QACrDN,OAAO,CAACO,KAAK,CAAC,kBAAkB,GAAAyC,gBAAA,GAAEzC,KAAK,CAACL,QAAQ,cAAA8C,gBAAA,uBAAdA,gBAAA,CAAgB3C,MAAM,CAAC;QACzDL,OAAO,CAACO,KAAK,CAAC,cAAc,GAAA0C,cAAA,GAAE1C,KAAK,CAAC2B,MAAM,cAAAe,cAAA,uBAAZA,cAAA,CAAcd,GAAG,CAAC;;QAEhD;QACA,IAAI5B,KAAK,CAAC6B,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC9B,KAAK,CAACL,QAAQ,EAAE;UAC9DF,OAAO,CAACO,KAAK,CAAC,uDAAuD,CAAC;QACxE;MACF;MACA,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA2C,yBAAyB,EAAE,MAAAA,CAAOtC,SAAe,EAAEC,OAAa,KAAkC;IAChG,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,MAAMf,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACe,WAAW,EAAE;QAChBd,OAAO,CAACO,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIQ,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACL,SAAS,CAAC;MACxD,MAAMM,gBAAgB,GAAGD,kBAAkB,CAACJ,OAAO,CAAC;;MAEpD;MACAb,OAAO,CAACC,GAAG,CAAC,gBAAgBH,QAAQ,4BAA4BkB,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MACjHlB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QAAEW,SAAS,EAAEI,kBAAkB;QAAEH,OAAO,EAAEK;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIC,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAM5B,GAAG,CAChB,GAAGO,QAAQ,4BAA4BkB,kBAAkB,YAAYE,gBAAgB,EAAE,EACvF;UACEf,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOgB,YAAY,EAAE;QACrBpB,OAAO,CAACO,KAAK,CAAC,yCAAyC,EAAEa,YAAY,CAAC;QACtEpB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMO,cAAc,GAAG,MAAMhB,KAAK,CAACD,GAAG,CACpC,yEAAyEyB,kBAAkB,YAAYE,gBAAgB,EAAE,EACzH;UACEf,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDe,MAAM,GAAGX,cAAc,CAACF,IAAI;MAC9B;MAEAN,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEkB,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXnB,OAAO,CAACO,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIc,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMI,cAAc,GAAGJ,MAAM,CAACK,GAAG,CAAC2B,IAAI,IAAI;YACxC;YACA,IAAI,CAACA,IAAI,EAAE,OAAO7D,yBAAyB,CAAC,IAAI,CAAC;YACjD,OAAOA,yBAAyB,CAAC6D,IAAI,CAAC;UACxC,CAAC,CAAC,CACDzB,MAAM,CAACyB,IAAI,IAAIA,IAAI,KAAK,IAAI;UAC7B;UAAA,CACCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACd,MAAMC,KAAK,GAAG,IAAIT,IAAI,CAACO,CAAC,CAACG,IAAI,CAAC;YAC9B,MAAMC,KAAK,GAAG,IAAIX,IAAI,CAACQ,CAAC,CAACE,IAAI,CAAC;YAC9B,OAAOD,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGD,KAAK,CAACC,OAAO,CAAC,CAAC;UAC1C,CAAC,CAAC;UAEF1D,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEsB,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZ3B,OAAO,CAACO,KAAK,CAAC,mCAAmC,EAAEoB,GAAG,CAAC;UACvD,OAAO,EAAE;QACX;MACF;;MAEA;MACA3B,OAAO,CAACO,KAAK,CAAC,6BAA6B,EAAEY,MAAM,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAoD,0BAA0B,EAAE,MAAAA,CAAO/C,SAAe,EAAEC,OAAa,KAAkC;IACjG,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,MAAMf,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACe,WAAW,EAAE;QAChBd,OAAO,CAACO,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIQ,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACL,SAAS,CAAC;MACxD,MAAMM,gBAAgB,GAAGD,kBAAkB,CAACJ,OAAO,CAAC;;MAEpD;MACAb,OAAO,CAACC,GAAG,CAAC,gBAAgBH,QAAQ,6BAA6BkB,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MAClHlB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QAAEW,SAAS,EAAEI,kBAAkB;QAAEH,OAAO,EAAEK;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIC,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAM5B,GAAG,CAChB,GAAGO,QAAQ,6BAA6BkB,kBAAkB,YAAYE,gBAAgB,EAAE,EACxF;UACEf,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOgB,YAAY,EAAE;QACrBpB,OAAO,CAACO,KAAK,CAAC,yCAAyC,EAAEa,YAAY,CAAC;QACtEpB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMO,cAAc,GAAG,MAAMhB,KAAK,CAACD,GAAG,CACpC,0EAA0EyB,kBAAkB,YAAYE,gBAAgB,EAAE,EAC1H;UACEf,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDe,MAAM,GAAGX,cAAc,CAACF,IAAI;MAC9B;MAEAN,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEkB,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXnB,OAAO,CAACO,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIc,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMI,cAAc,GAAGJ,MAAM,CAACK,GAAG,CAAC2B,IAAI,IAAI;YACxC;YACA,IAAI,CAACA,IAAI,EAAE,OAAO7D,yBAAyB,CAAC,IAAI,CAAC;YACjD,OAAOA,yBAAyB,CAAC6D,IAAI,CAAC;UACxC,CAAC,CAAC,CAACzB,MAAM,CAACyB,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC;UAEhCnD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEsB,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZ3B,OAAO,CAACO,KAAK,CAAC,oCAAoC,EAAEoB,GAAG,CAAC;UACxD,OAAO,EAAE;QACX;MACF;;MAEA;MACA3B,OAAO,CAACO,KAAK,CAAC,6BAA6B,EAAEY,MAAM,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAqD,2BAA2B,EAAE,MAAAA,CAAOhD,SAAe,EAAEC,OAAa,KAAkC;IAClG,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,MAAMf,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACe,WAAW,EAAE;QAChBd,OAAO,CAACO,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIQ,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACL,SAAS,CAAC;MACxD,MAAMM,gBAAgB,GAAGD,kBAAkB,CAACJ,OAAO,CAAC;;MAEpD;MACAb,OAAO,CAACC,GAAG,CAAC,gBAAgBH,QAAQ,8BAA8BkB,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MACnHlB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QAAEW,SAAS,EAAEI,kBAAkB;QAAEH,OAAO,EAAEK;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIC,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAM5B,GAAG,CAChB,GAAGO,QAAQ,8BAA8BkB,kBAAkB,YAAYE,gBAAgB,EAAE,EACzF;UACEf,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOgB,YAAY,EAAE;QACrBpB,OAAO,CAACO,KAAK,CAAC,yCAAyC,EAAEa,YAAY,CAAC;QACtEpB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMO,cAAc,GAAG,MAAMhB,KAAK,CAACD,GAAG,CACpC,2EAA2EyB,kBAAkB,YAAYE,gBAAgB,EAAE,EAC3H;UACEf,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDe,MAAM,GAAGX,cAAc,CAACF,IAAI;MAC9B;MAEAN,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEkB,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXnB,OAAO,CAACO,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIc,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMI,cAAc,GAAGJ,MAAM,CAACK,GAAG,CAAC2B,IAAI,IAAI;YACxC;YACA,IAAI,CAACA,IAAI,EAAE,OAAO7D,yBAAyB,CAAC,IAAI,CAAC;YACjD,OAAOA,yBAAyB,CAAC6D,IAAI,CAAC;UACxC,CAAC,CAAC,CACDzB,MAAM,CAACyB,IAAI,IAAIA,IAAI,KAAK,IAAI;UAC7B;UAAA,CACCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACd,MAAMC,KAAK,GAAG,IAAIT,IAAI,CAACO,CAAC,CAACG,IAAI,CAAC;YAC9B,MAAMC,KAAK,GAAG,IAAIX,IAAI,CAACQ,CAAC,CAACE,IAAI,CAAC;YAC9B,OAAOD,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGD,KAAK,CAACC,OAAO,CAAC,CAAC;UAC1C,CAAC,CAAC;UAEF1D,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEsB,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZ3B,OAAO,CAACO,KAAK,CAAC,qCAAqC,EAAEoB,GAAG,CAAC;UACzD,OAAO,EAAE;QACX;MACF;;MAEA;MACA3B,OAAO,CAACO,KAAK,CAAC,6BAA6B,EAAEY,MAAM,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAsD,0BAA0B,EAAE,MAAAA,CAAOjD,SAAe,EAAEC,OAAa,KAAkC;IACjG,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,MAAMf,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACe,WAAW,EAAE;QAChBd,OAAO,CAACO,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIQ,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACL,SAAS,CAAC;MACxD,MAAMM,gBAAgB,GAAGD,kBAAkB,CAACJ,OAAO,CAAC;;MAEpD;MACAb,OAAO,CAACC,GAAG,CAAC,gBAAgBH,QAAQ,6BAA6BkB,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MAClHlB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QAAEW,SAAS,EAAEI,kBAAkB;QAAEH,OAAO,EAAEK;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIC,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAM5B,GAAG,CAChB,GAAGO,QAAQ,6BAA6BkB,kBAAkB,YAAYE,gBAAgB,EAAE,EACxF;UACEf,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOgB,YAAY,EAAE;QACrBpB,OAAO,CAACO,KAAK,CAAC,yCAAyC,EAAEa,YAAY,CAAC;QACtEpB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMO,cAAc,GAAG,MAAMhB,KAAK,CAACD,GAAG,CACpC,0EAA0EyB,kBAAkB,YAAYE,gBAAgB,EAAE,EAC1H;UACEf,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDe,MAAM,GAAGX,cAAc,CAACF,IAAI;MAC9B;MAEAN,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEkB,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXnB,OAAO,CAACO,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIc,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMI,cAAc,GAAGJ,MAAM,CAACK,GAAG,CAAC2B,IAAI,IAAI;YACxC;YACA,IAAI,CAACA,IAAI,EAAE,OAAO7D,yBAAyB,CAAC,IAAI,CAAC;YACjD,OAAOA,yBAAyB,CAAC6D,IAAI,CAAC;UACxC,CAAC,CAAC,CACDzB,MAAM,CAACyB,IAAI,IAAIA,IAAI,KAAK,IAAI;UAC7B;UAAA,CACCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACd,MAAMC,KAAK,GAAG,IAAIT,IAAI,CAACO,CAAC,CAACG,IAAI,CAAC;YAC9B,MAAMC,KAAK,GAAG,IAAIX,IAAI,CAACQ,CAAC,CAACE,IAAI,CAAC;YAC9B,OAAOD,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGD,KAAK,CAACC,OAAO,CAAC,CAAC;UAC1C,CAAC,CAAC;UAEF1D,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEsB,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZ3B,OAAO,CAACO,KAAK,CAAC,oCAAoC,EAAEoB,GAAG,CAAC;UACxD,OAAO,EAAE;QACX;MACF;;MAEA;MACA3B,OAAO,CAACO,KAAK,CAAC,6BAA6B,EAAEY,MAAM,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;AACF,CAAC;;AAED;AACA,MAAMU,kBAAkB,GAAIuC,IAAU,IAAa;EACjD,IAAI;IACF;IACA,IAAI,EAAEA,IAAI,YAAYV,IAAI,CAAC,IAAIgB,KAAK,CAACN,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;MACpD1D,OAAO,CAACO,KAAK,CAAC,wBAAwB,EAAEiD,IAAI,CAAC;MAC7C;MACA,MAAMO,KAAK,GAAG,IAAIjB,IAAI,CAAC,CAAC;MACxB,OAAOrD,kBAAkB,CAACsE,KAAK,CAAC;IAClC;;IAEA;IACA;IACA,MAAMC,YAAY,GAAG,IAAIlB,IAAI,CAC3BU,IAAI,CAACS,WAAW,CAAC,CAAC,EAClBT,IAAI,CAACU,QAAQ,CAAC,CAAC,EACfV,IAAI,CAACW,OAAO,CAAC,CAAC,EACd,EAAE,EAAE,CAAC,EAAE,CACT,CAAC;;IAED;IACA,OAAO1E,kBAAkB,CAACuE,YAAY,CAAC;EACzC,CAAC,CAAC,OAAOzD,KAAK,EAAE;IACdP,OAAO,CAACO,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C;IACA,MAAMwD,KAAK,GAAG,IAAIjB,IAAI,CAAC,CAAC;IACxB,OAAOrD,kBAAkB,CAACsE,KAAK,CAAC;EAClC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}