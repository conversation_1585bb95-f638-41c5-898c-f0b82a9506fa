{"ast": null, "code": "import axios from 'axios';\n\n// Kiểm tra môi trường và cấu hình URL phù hợp\nconst getBaseUrl = () => {\n  // Nếu đang chạy trong môi trường phát triển (localhost)\n  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {\n    // Kiểm tra xem API Gateway có hoạt động không\n    const apiGatewayUrl = process.env.REACT_APP_API_URL || 'http://localhost:8080';\n    console.log('Using API Gateway URL:', apiGatewayUrl);\n    return apiGatewayUrl;\n  }\n\n  // Nếu đang chạy trong môi trường production, sử dụng URL tương đối\n  return '';\n};\n\n// Kiểm tra xem API Gateway có hoạt động không\nconst checkApiGateway = async () => {\n  try {\n    const response = await axios.get(`${getBaseUrl()}/actuator/health`, {\n      timeout: 3000,\n      headers: {\n        'Accept': 'application/json'\n      }\n    });\n    return response.status === 200;\n  } catch (error) {\n    console.error('API Gateway health check failed:', error);\n    return false;\n  }\n};\n\n// Create a base API client instance\nconst apiClient = axios.create({\n  baseURL: getBaseUrl(),\n  // API gateway URL\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n    'X-Requested-With': 'XMLHttpRequest' // Giúp một số máy chủ nhận biết đây là AJAX request\n  },\n  timeout: 30000,\n  // Tăng timeout lên 30 seconds\n  withCredentials: false // Không gửi cookie trong cross-origin requests\n});\n\n// Request interceptor for API calls\napiClient.interceptors.request.use(config => {\n  var _config$method;\n  // Log the request for debugging\n  console.log(`API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`);\n  return config;\n}, error => {\n  console.error('Request error:', error);\n  return Promise.reject(error);\n});\n\n// Response interceptor for API calls\napiClient.interceptors.response.use(response => {\n  // Log successful responses for debugging\n  console.log(`API Response: ${response.status} ${response.config.url}`);\n\n  // Kiểm tra nếu response là JSON hợp lệ\n  try {\n    if (typeof response.data === 'string' && response.data.trim() !== '') {\n      console.warn('Response is string, attempting to parse as JSON:', response.data);\n      response.data = JSON.parse(response.data);\n    }\n  } catch (e) {\n    console.error('Failed to parse response data as JSON:', e);\n  }\n  return response;\n}, error => {\n  // Handle errors globally\n  if (axios.isAxiosError(error)) {\n    var _error$response, _error$config, _error$config2, _error$response2, _error$response3, _error$response4, _error$response5;\n    const errorInfo = {\n      message: error.message,\n      status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n      url: (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url,\n      method: (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.method,\n      data: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data,\n      code: error.code\n    };\n    console.error('API Error:', errorInfo);\n\n    // Chi tiết hơn về các loại lỗi\n    if (error.code === 'ECONNABORTED') {\n      console.error('Request timeout. The server took too long to respond.');\n      error.message = 'Yêu cầu đã hết thời gian chờ. Máy chủ mất quá nhiều thời gian để phản hồi.';\n    } else if (error.message.includes('Network Error') || !error.response) {\n      console.error('Network error. Please check your connection or the server might be down.');\n\n      // Kiểm tra lỗi CORS\n      if (error.message.includes('CORS')) {\n        console.error('CORS error detected. This might be a cross-origin issue.');\n        error.message = 'Lỗi CORS: Không thể kết nối đến máy chủ do chính sách bảo mật trình duyệt.';\n      } else {\n        error.message = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối của bạn hoặc máy chủ có thể đang gặp sự cố.';\n      }\n    } else if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 404) {\n      error.message = 'Không tìm thấy tài nguyên yêu cầu.';\n    } else if (((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status) === 500) {\n      error.message = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';\n    } else if (((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status) === 403) {\n      error.message = 'Bạn không có quyền truy cập tài nguyên này.';\n    }\n  } else {\n    console.error('Unexpected error:', error);\n  }\n  return Promise.reject(error);\n});\n\n// Generic GET request\nexport const get = async (url, config) => {\n  try {\n    // Thêm log để debug\n    console.log(`Making GET request to: ${getBaseUrl()}${url}`, config);\n\n    // Thử gọi API qua API Gateway\n    try {\n      const response = await apiClient.get(url, config);\n      console.log(`GET request to ${url} succeeded with status:`, response.status);\n      return response.data;\n    } catch (gatewayError) {\n      console.error(`GET request failed through API Gateway for ${url}:`, gatewayError);\n\n      // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\n      let directUrl = null;\n      if (url.startsWith('/api/customer-statistics')) {\n        directUrl = `http://localhost:8085${url}`;\n        console.log(`Trying direct connection to customer-statistics-service for ${url}`);\n      } else if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {\n        directUrl = `http://localhost:8083${url}`;\n        console.log(`Trying direct connection to customer-contract-service for ${url}`);\n      } else if (url.startsWith('/api/customer-payment')) {\n        directUrl = `http://localhost:8084${url}`;\n        console.log(`Trying direct connection to customer-payment-service for ${url}`);\n      } else if (url.startsWith('/api/customer')) {\n        directUrl = `http://localhost:8081${url}`;\n        console.log(`Trying direct connection to customer-service for ${url}`);\n      } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {\n        directUrl = `http://localhost:8082${url}`;\n        console.log(`Trying direct connection to job-service for ${url}`);\n      }\n      if (directUrl) {\n        console.log(`Direct URL: ${directUrl}`);\n\n        // Gọi trực tiếp đến service\n        const directResponse = await axios.get(directUrl, {\n          ...config,\n          headers: {\n            ...(config === null || config === void 0 ? void 0 : config.headers),\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        console.log(`Direct GET request to ${directUrl} succeeded with status:`, directResponse.status);\n        return directResponse.data;\n      }\n\n      // Nếu không có service phù hợp, ném lỗi\n      throw gatewayError;\n    }\n  } catch (error) {\n    console.error(`GET request failed for ${url}:`, error);\n    throw error;\n  }\n};\n\n// Generic POST request\nexport const post = async (url, data, config) => {\n  try {\n    const response = await apiClient.post(url, data, config);\n    return response.data;\n  } catch (gatewayError) {\n    console.error(`POST request failed through API Gateway for ${url}:`, gatewayError);\n\n    // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\n    let directUrl = null;\n    if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {\n      directUrl = `http://localhost:8083${url}`;\n    } else if (url.startsWith('/api/customer-payment')) {\n      directUrl = `http://localhost:8084${url}`;\n    } else if (url.startsWith('/api/customer')) {\n      directUrl = `http://localhost:8081${url}`;\n    } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {\n      directUrl = `http://localhost:8082${url}`;\n    }\n    if (directUrl) {\n      const directResponse = await axios.post(directUrl, data, {\n        ...config,\n        headers: {\n          ...(config === null || config === void 0 ? void 0 : config.headers),\n          'Accept': 'application/json',\n          'Content-Type': 'application/json'\n        }\n      });\n      return directResponse.data;\n    }\n    throw gatewayError;\n  }\n};\n\n// Generic PUT request\nexport const put = async (url, data, config) => {\n  try {\n    const response = await apiClient.put(url, data, config);\n    return response.data;\n  } catch (gatewayError) {\n    console.error(`PUT request failed through API Gateway for ${url}:`, gatewayError);\n\n    // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\n    let directUrl = null;\n    if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {\n      directUrl = `http://localhost:8083${url}`;\n    } else if (url.startsWith('/api/customer-payment')) {\n      directUrl = `http://localhost:8084${url}`;\n    } else if (url.startsWith('/api/customer')) {\n      directUrl = `http://localhost:8081${url}`;\n    } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {\n      directUrl = `http://localhost:8082${url}`;\n    }\n    if (directUrl) {\n      const directResponse = await axios.put(directUrl, data, {\n        ...config,\n        headers: {\n          ...(config === null || config === void 0 ? void 0 : config.headers),\n          'Accept': 'application/json',\n          'Content-Type': 'application/json'\n        }\n      });\n      return directResponse.data;\n    }\n    throw gatewayError;\n  }\n};\n\n// Generic DELETE request\nexport const del = async (url, config) => {\n  try {\n    const response = await apiClient.delete(url, config);\n    return response.data;\n  } catch (gatewayError) {\n    console.error(`DELETE request failed through API Gateway for ${url}:`, gatewayError);\n\n    // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\n    let directUrl = null;\n    if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {\n      directUrl = `http://localhost:8083${url}`;\n    } else if (url.startsWith('/api/customer-payment')) {\n      directUrl = `http://localhost:8084${url}`;\n    } else if (url.startsWith('/api/customer')) {\n      directUrl = `http://localhost:8081${url}`;\n    } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {\n      directUrl = `http://localhost:8082${url}`;\n    }\n    if (directUrl) {\n      const directResponse = await axios.delete(directUrl, {\n        ...config,\n        headers: {\n          ...(config === null || config === void 0 ? void 0 : config.headers),\n          'Accept': 'application/json',\n          'Content-Type': 'application/json'\n        }\n      });\n      return directResponse.data;\n    }\n    throw gatewayError;\n  }\n};\nexport default apiClient;", "map": {"version": 3, "names": ["axios", "getBaseUrl", "window", "location", "hostname", "apiGatewayUrl", "process", "env", "REACT_APP_API_URL", "console", "log", "checkApiGateway", "response", "get", "timeout", "headers", "status", "error", "apiClient", "create", "baseURL", "withCredentials", "interceptors", "request", "use", "config", "_config$method", "method", "toUpperCase", "url", "Promise", "reject", "data", "trim", "warn", "JSON", "parse", "e", "isAxiosError", "_error$response", "_error$config", "_error$config2", "_error$response2", "_error$response3", "_error$response4", "_error$response5", "errorInfo", "message", "code", "includes", "gatewayError", "directUrl", "startsWith", "directResponse", "post", "put", "del", "delete"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/services/api/apiClient.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\n\n// Kiểm tra môi trường và cấu hình URL phù hợp\nconst getBaseUrl = () => {\n  // Nếu đang chạy trong môi trường phát triển (localhost)\n  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {\n    // Kiểm tra xem API Gateway có hoạt động không\n    const apiGatewayUrl = process.env.REACT_APP_API_URL || 'http://localhost:8080';\n    console.log('Using API Gateway URL:', apiGatewayUrl);\n    return apiGatewayUrl;\n  }\n\n  // Nếu đang chạy trong môi trường production, sử dụng URL tương đối\n  return '';\n};\n\n// Kiểm tra xem API Gateway có hoạt động không\nconst checkApiGateway = async (): Promise<boolean> => {\n  try {\n    const response = await axios.get(`${getBaseUrl()}/actuator/health`, {\n      timeout: 3000,\n      headers: {\n        'Accept': 'application/json'\n      }\n    });\n    return response.status === 200;\n  } catch (error) {\n    console.error('API Gateway health check failed:', error);\n    return false;\n  }\n};\n\n// Create a base API client instance\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: getBaseUrl(), // API gateway URL\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n    'X-Requested-With': 'XMLHttpRequest' // Giúp một số máy chủ nhận biết đây là AJAX request\n  },\n  timeout: 30000, // Tăng timeout lên 30 seconds\n  withCredentials: false // Không gửi cookie trong cross-origin requests\n});\n\n// Request interceptor for API calls\napiClient.interceptors.request.use(\n  (config) => {\n    // Log the request for debugging\n    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    return config;\n  },\n  (error) => {\n    console.error('Request error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for API calls\napiClient.interceptors.response.use(\n  (response) => {\n    // Log successful responses for debugging\n    console.log(`API Response: ${response.status} ${response.config.url}`);\n\n    // Kiểm tra nếu response là JSON hợp lệ\n    try {\n      if (typeof response.data === 'string' && response.data.trim() !== '') {\n        console.warn('Response is string, attempting to parse as JSON:', response.data);\n        response.data = JSON.parse(response.data);\n      }\n    } catch (e) {\n      console.error('Failed to parse response data as JSON:', e);\n    }\n\n    return response;\n  },\n  (error) => {\n    // Handle errors globally\n    if (axios.isAxiosError(error)) {\n      const errorInfo = {\n        message: error.message,\n        status: error.response?.status,\n        url: error.config?.url,\n        method: error.config?.method,\n        data: error.response?.data,\n        code: error.code\n      };\n\n      console.error('API Error:', errorInfo);\n\n      // Chi tiết hơn về các loại lỗi\n      if (error.code === 'ECONNABORTED') {\n        console.error('Request timeout. The server took too long to respond.');\n        error.message = 'Yêu cầu đã hết thời gian chờ. Máy chủ mất quá nhiều thời gian để phản hồi.';\n      } else if (error.message.includes('Network Error') || !error.response) {\n        console.error('Network error. Please check your connection or the server might be down.');\n\n        // Kiểm tra lỗi CORS\n        if (error.message.includes('CORS')) {\n          console.error('CORS error detected. This might be a cross-origin issue.');\n          error.message = 'Lỗi CORS: Không thể kết nối đến máy chủ do chính sách bảo mật trình duyệt.';\n        } else {\n          error.message = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối của bạn hoặc máy chủ có thể đang gặp sự cố.';\n        }\n      } else if (error.response?.status === 404) {\n        error.message = 'Không tìm thấy tài nguyên yêu cầu.';\n      } else if (error.response?.status === 500) {\n        error.message = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';\n      } else if (error.response?.status === 403) {\n        error.message = 'Bạn không có quyền truy cập tài nguyên này.';\n      }\n    } else {\n      console.error('Unexpected error:', error);\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Generic GET request\nexport const get = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n  try {\n    // Thêm log để debug\n    console.log(`Making GET request to: ${getBaseUrl()}${url}`, config);\n\n    // Thử gọi API qua API Gateway\n    try {\n      const response: AxiosResponse<T> = await apiClient.get(url, config);\n      console.log(`GET request to ${url} succeeded with status:`, response.status);\n      return response.data;\n    } catch (gatewayError) {\n      console.error(`GET request failed through API Gateway for ${url}:`, gatewayError);\n\n      // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\n      let directUrl: string | null = null;\n\n      if (url.startsWith('/api/customer-statistics')) {\n        directUrl = `http://localhost:8085${url}`;\n        console.log(`Trying direct connection to customer-statistics-service for ${url}`);\n      } else if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {\n        directUrl = `http://localhost:8083${url}`;\n        console.log(`Trying direct connection to customer-contract-service for ${url}`);\n      } else if (url.startsWith('/api/customer-payment')) {\n        directUrl = `http://localhost:8084${url}`;\n        console.log(`Trying direct connection to customer-payment-service for ${url}`);\n      } else if (url.startsWith('/api/customer')) {\n        directUrl = `http://localhost:8081${url}`;\n        console.log(`Trying direct connection to customer-service for ${url}`);\n      } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {\n        directUrl = `http://localhost:8082${url}`;\n        console.log(`Trying direct connection to job-service for ${url}`);\n      }\n\n      if (directUrl) {\n        console.log(`Direct URL: ${directUrl}`);\n\n        // Gọi trực tiếp đến service\n        const directResponse = await axios.get(directUrl, {\n          ...config,\n          headers: {\n            ...config?.headers,\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n\n        console.log(`Direct GET request to ${directUrl} succeeded with status:`, directResponse.status);\n        return directResponse.data;\n      }\n\n      // Nếu không có service phù hợp, ném lỗi\n      throw gatewayError;\n    }\n  } catch (error) {\n    console.error(`GET request failed for ${url}:`, error);\n    throw error;\n  }\n};\n\n// Generic POST request\nexport const post = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n  try {\n    const response: AxiosResponse<T> = await apiClient.post(url, data, config);\n    return response.data;\n  } catch (gatewayError) {\n    console.error(`POST request failed through API Gateway for ${url}:`, gatewayError);\n\n    // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\n    let directUrl: string | null = null;\n\n    if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {\n      directUrl = `http://localhost:8083${url}`;\n    } else if (url.startsWith('/api/customer-payment')) {\n      directUrl = `http://localhost:8084${url}`;\n    } else if (url.startsWith('/api/customer')) {\n      directUrl = `http://localhost:8081${url}`;\n    } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {\n      directUrl = `http://localhost:8082${url}`;\n    }\n\n    if (directUrl) {\n      const directResponse = await axios.post(directUrl, data, {\n        ...config,\n        headers: {\n          ...config?.headers,\n          'Accept': 'application/json',\n          'Content-Type': 'application/json'\n        }\n      });\n      return directResponse.data;\n    }\n\n    throw gatewayError;\n  }\n};\n\n// Generic PUT request\nexport const put = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n  try {\n    const response: AxiosResponse<T> = await apiClient.put(url, data, config);\n    return response.data;\n  } catch (gatewayError) {\n    console.error(`PUT request failed through API Gateway for ${url}:`, gatewayError);\n\n    // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\n    let directUrl: string | null = null;\n\n    if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {\n      directUrl = `http://localhost:8083${url}`;\n    } else if (url.startsWith('/api/customer-payment')) {\n      directUrl = `http://localhost:8084${url}`;\n    } else if (url.startsWith('/api/customer')) {\n      directUrl = `http://localhost:8081${url}`;\n    } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {\n      directUrl = `http://localhost:8082${url}`;\n    }\n\n    if (directUrl) {\n      const directResponse = await axios.put(directUrl, data, {\n        ...config,\n        headers: {\n          ...config?.headers,\n          'Accept': 'application/json',\n          'Content-Type': 'application/json'\n        }\n      });\n      return directResponse.data;\n    }\n\n    throw gatewayError;\n  }\n};\n\n// Generic DELETE request\nexport const del = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n  try {\n    const response: AxiosResponse<T> = await apiClient.delete(url, config);\n    return response.data;\n  } catch (gatewayError) {\n    console.error(`DELETE request failed through API Gateway for ${url}:`, gatewayError);\n\n    // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại\n    let directUrl: string | null = null;\n\n    if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {\n      directUrl = `http://localhost:8083${url}`;\n    } else if (url.startsWith('/api/customer-payment')) {\n      directUrl = `http://localhost:8084${url}`;\n    } else if (url.startsWith('/api/customer')) {\n      directUrl = `http://localhost:8081${url}`;\n    } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {\n      directUrl = `http://localhost:8082${url}`;\n    }\n\n    if (directUrl) {\n      const directResponse = await axios.delete(directUrl, {\n        ...config,\n        headers: {\n          ...config?.headers,\n          'Accept': 'application/json',\n          'Content-Type': 'application/json'\n        }\n      });\n      return directResponse.data;\n    }\n\n    throw gatewayError;\n  }\n};\n\nexport default apiClient;\n"], "mappings": "AAAA,OAAOA,KAAK,MAA4D,OAAO;;AAE/E;AACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB;EACA,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW,IAAIF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW,EAAE;IACxF;IACA,MAAMC,aAAa,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;IAC9EC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEL,aAAa,CAAC;IACpD,OAAOA,aAAa;EACtB;;EAEA;EACA,OAAO,EAAE;AACX,CAAC;;AAED;AACA,MAAMM,eAAe,GAAG,MAAAA,CAAA,KAA8B;EACpD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMZ,KAAK,CAACa,GAAG,CAAC,GAAGZ,UAAU,CAAC,CAAC,kBAAkB,EAAE;MAClEa,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;QACP,QAAQ,EAAE;MACZ;IACF,CAAC,CAAC;IACF,OAAOH,QAAQ,CAACI,MAAM,KAAK,GAAG;EAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdR,OAAO,CAACQ,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAMC,SAAwB,GAAGlB,KAAK,CAACmB,MAAM,CAAC;EAC5CC,OAAO,EAAEnB,UAAU,CAAC,CAAC;EAAE;EACvBc,OAAO,EAAE;IACP,cAAc,EAAE,kBAAkB;IAClC,QAAQ,EAAE,kBAAkB;IAC5B,kBAAkB,EAAE,gBAAgB,CAAC;EACvC,CAAC;EACDD,OAAO,EAAE,KAAK;EAAE;EAChBO,eAAe,EAAE,KAAK,CAAC;AACzB,CAAC,CAAC;;AAEF;AACAH,SAAS,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/BC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACV;EACAjB,OAAO,CAACC,GAAG,CAAC,iBAAAgB,cAAA,GAAgBD,MAAM,CAACE,MAAM,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,WAAW,CAAC,CAAC,IAAIH,MAAM,CAACI,GAAG,EAAE,CAAC;EACzE,OAAOJ,MAAM;AACf,CAAC,EACAR,KAAK,IAAK;EACTR,OAAO,CAACQ,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;EACtC,OAAOa,OAAO,CAACC,MAAM,CAACd,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAC,SAAS,CAACI,YAAY,CAACV,QAAQ,CAACY,GAAG,CAChCZ,QAAQ,IAAK;EACZ;EACAH,OAAO,CAACC,GAAG,CAAC,iBAAiBE,QAAQ,CAACI,MAAM,IAAIJ,QAAQ,CAACa,MAAM,CAACI,GAAG,EAAE,CAAC;;EAEtE;EACA,IAAI;IACF,IAAI,OAAOjB,QAAQ,CAACoB,IAAI,KAAK,QAAQ,IAAIpB,QAAQ,CAACoB,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACpExB,OAAO,CAACyB,IAAI,CAAC,kDAAkD,EAAEtB,QAAQ,CAACoB,IAAI,CAAC;MAC/EpB,QAAQ,CAACoB,IAAI,GAAGG,IAAI,CAACC,KAAK,CAACxB,QAAQ,CAACoB,IAAI,CAAC;IAC3C;EACF,CAAC,CAAC,OAAOK,CAAC,EAAE;IACV5B,OAAO,CAACQ,KAAK,CAAC,wCAAwC,EAAEoB,CAAC,CAAC;EAC5D;EAEA,OAAOzB,QAAQ;AACjB,CAAC,EACAK,KAAK,IAAK;EACT;EACA,IAAIjB,KAAK,CAACsC,YAAY,CAACrB,KAAK,CAAC,EAAE;IAAA,IAAAsB,eAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;IAC7B,MAAMC,SAAS,GAAG;MAChBC,OAAO,EAAE9B,KAAK,CAAC8B,OAAO;MACtB/B,MAAM,GAAAuB,eAAA,GAAEtB,KAAK,CAACL,QAAQ,cAAA2B,eAAA,uBAAdA,eAAA,CAAgBvB,MAAM;MAC9Ba,GAAG,GAAAW,aAAA,GAAEvB,KAAK,CAACQ,MAAM,cAAAe,aAAA,uBAAZA,aAAA,CAAcX,GAAG;MACtBF,MAAM,GAAAc,cAAA,GAAExB,KAAK,CAACQ,MAAM,cAAAgB,cAAA,uBAAZA,cAAA,CAAcd,MAAM;MAC5BK,IAAI,GAAAU,gBAAA,GAAEzB,KAAK,CAACL,QAAQ,cAAA8B,gBAAA,uBAAdA,gBAAA,CAAgBV,IAAI;MAC1BgB,IAAI,EAAE/B,KAAK,CAAC+B;IACd,CAAC;IAEDvC,OAAO,CAACQ,KAAK,CAAC,YAAY,EAAE6B,SAAS,CAAC;;IAEtC;IACA,IAAI7B,KAAK,CAAC+B,IAAI,KAAK,cAAc,EAAE;MACjCvC,OAAO,CAACQ,KAAK,CAAC,uDAAuD,CAAC;MACtEA,KAAK,CAAC8B,OAAO,GAAG,4EAA4E;IAC9F,CAAC,MAAM,IAAI9B,KAAK,CAAC8B,OAAO,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAChC,KAAK,CAACL,QAAQ,EAAE;MACrEH,OAAO,CAACQ,KAAK,CAAC,0EAA0E,CAAC;;MAEzF;MACA,IAAIA,KAAK,CAAC8B,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;QAClCxC,OAAO,CAACQ,KAAK,CAAC,0DAA0D,CAAC;QACzEA,KAAK,CAAC8B,OAAO,GAAG,4EAA4E;MAC9F,CAAC,MAAM;QACL9B,KAAK,CAAC8B,OAAO,GAAG,yFAAyF;MAC3G;IACF,CAAC,MAAM,IAAI,EAAAJ,gBAAA,GAAA1B,KAAK,CAACL,QAAQ,cAAA+B,gBAAA,uBAAdA,gBAAA,CAAgB3B,MAAM,MAAK,GAAG,EAAE;MACzCC,KAAK,CAAC8B,OAAO,GAAG,oCAAoC;IACtD,CAAC,MAAM,IAAI,EAAAH,gBAAA,GAAA3B,KAAK,CAACL,QAAQ,cAAAgC,gBAAA,uBAAdA,gBAAA,CAAgB5B,MAAM,MAAK,GAAG,EAAE;MACzCC,KAAK,CAAC8B,OAAO,GAAG,2CAA2C;IAC7D,CAAC,MAAM,IAAI,EAAAF,gBAAA,GAAA5B,KAAK,CAACL,QAAQ,cAAAiC,gBAAA,uBAAdA,gBAAA,CAAgB7B,MAAM,MAAK,GAAG,EAAE;MACzCC,KAAK,CAAC8B,OAAO,GAAG,6CAA6C;IAC/D;EACF,CAAC,MAAM;IACLtC,OAAO,CAACQ,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;EAC3C;EACA,OAAOa,OAAO,CAACC,MAAM,CAACd,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMJ,GAAG,GAAG,MAAAA,CAAUgB,GAAW,EAAEJ,MAA2B,KAAiB;EACpF,IAAI;IACF;IACAhB,OAAO,CAACC,GAAG,CAAC,0BAA0BT,UAAU,CAAC,CAAC,GAAG4B,GAAG,EAAE,EAAEJ,MAAM,CAAC;;IAEnE;IACA,IAAI;MACF,MAAMb,QAA0B,GAAG,MAAMM,SAAS,CAACL,GAAG,CAACgB,GAAG,EAAEJ,MAAM,CAAC;MACnEhB,OAAO,CAACC,GAAG,CAAC,kBAAkBmB,GAAG,yBAAyB,EAAEjB,QAAQ,CAACI,MAAM,CAAC;MAC5E,OAAOJ,QAAQ,CAACoB,IAAI;IACtB,CAAC,CAAC,OAAOkB,YAAY,EAAE;MACrBzC,OAAO,CAACQ,KAAK,CAAC,8CAA8CY,GAAG,GAAG,EAAEqB,YAAY,CAAC;;MAEjF;MACA,IAAIC,SAAwB,GAAG,IAAI;MAEnC,IAAItB,GAAG,CAACuB,UAAU,CAAC,0BAA0B,CAAC,EAAE;QAC9CD,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;QACzCpB,OAAO,CAACC,GAAG,CAAC,+DAA+DmB,GAAG,EAAE,CAAC;MACnF,CAAC,MAAM,IAAIA,GAAG,CAACuB,UAAU,CAAC,wBAAwB,CAAC,IAAIvB,GAAG,CAACuB,UAAU,CAAC,iBAAiB,CAAC,IAAIvB,GAAG,CAACuB,UAAU,CAAC,iBAAiB,CAAC,EAAE;QAC7HD,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;QACzCpB,OAAO,CAACC,GAAG,CAAC,6DAA6DmB,GAAG,EAAE,CAAC;MACjF,CAAC,MAAM,IAAIA,GAAG,CAACuB,UAAU,CAAC,uBAAuB,CAAC,EAAE;QAClDD,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;QACzCpB,OAAO,CAACC,GAAG,CAAC,4DAA4DmB,GAAG,EAAE,CAAC;MAChF,CAAC,MAAM,IAAIA,GAAG,CAACuB,UAAU,CAAC,eAAe,CAAC,EAAE;QAC1CD,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;QACzCpB,OAAO,CAACC,GAAG,CAAC,oDAAoDmB,GAAG,EAAE,CAAC;MACxE,CAAC,MAAM,IAAIA,GAAG,CAACuB,UAAU,CAAC,UAAU,CAAC,IAAIvB,GAAG,CAACuB,UAAU,CAAC,mBAAmB,CAAC,EAAE;QAC5ED,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;QACzCpB,OAAO,CAACC,GAAG,CAAC,+CAA+CmB,GAAG,EAAE,CAAC;MACnE;MAEA,IAAIsB,SAAS,EAAE;QACb1C,OAAO,CAACC,GAAG,CAAC,eAAeyC,SAAS,EAAE,CAAC;;QAEvC;QACA,MAAME,cAAc,GAAG,MAAMrD,KAAK,CAACa,GAAG,CAACsC,SAAS,EAAE;UAChD,GAAG1B,MAAM;UACTV,OAAO,EAAE;YACP,IAAGU,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEV,OAAO;YAClB,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEFN,OAAO,CAACC,GAAG,CAAC,yBAAyByC,SAAS,yBAAyB,EAAEE,cAAc,CAACrC,MAAM,CAAC;QAC/F,OAAOqC,cAAc,CAACrB,IAAI;MAC5B;;MAEA;MACA,MAAMkB,YAAY;IACpB;EACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;IACdR,OAAO,CAACQ,KAAK,CAAC,0BAA0BY,GAAG,GAAG,EAAEZ,KAAK,CAAC;IACtD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMqC,IAAI,GAAG,MAAAA,CAAUzB,GAAW,EAAEG,IAAU,EAAEP,MAA2B,KAAiB;EACjG,IAAI;IACF,MAAMb,QAA0B,GAAG,MAAMM,SAAS,CAACoC,IAAI,CAACzB,GAAG,EAAEG,IAAI,EAAEP,MAAM,CAAC;IAC1E,OAAOb,QAAQ,CAACoB,IAAI;EACtB,CAAC,CAAC,OAAOkB,YAAY,EAAE;IACrBzC,OAAO,CAACQ,KAAK,CAAC,+CAA+CY,GAAG,GAAG,EAAEqB,YAAY,CAAC;;IAElF;IACA,IAAIC,SAAwB,GAAG,IAAI;IAEnC,IAAItB,GAAG,CAACuB,UAAU,CAAC,wBAAwB,CAAC,IAAIvB,GAAG,CAACuB,UAAU,CAAC,iBAAiB,CAAC,IAAIvB,GAAG,CAACuB,UAAU,CAAC,iBAAiB,CAAC,EAAE;MACtHD,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;IAC3C,CAAC,MAAM,IAAIA,GAAG,CAACuB,UAAU,CAAC,uBAAuB,CAAC,EAAE;MAClDD,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;IAC3C,CAAC,MAAM,IAAIA,GAAG,CAACuB,UAAU,CAAC,eAAe,CAAC,EAAE;MAC1CD,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;IAC3C,CAAC,MAAM,IAAIA,GAAG,CAACuB,UAAU,CAAC,UAAU,CAAC,IAAIvB,GAAG,CAACuB,UAAU,CAAC,mBAAmB,CAAC,EAAE;MAC5ED,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;IAC3C;IAEA,IAAIsB,SAAS,EAAE;MACb,MAAME,cAAc,GAAG,MAAMrD,KAAK,CAACsD,IAAI,CAACH,SAAS,EAAEnB,IAAI,EAAE;QACvD,GAAGP,MAAM;QACTV,OAAO,EAAE;UACP,IAAGU,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEV,OAAO;UAClB,QAAQ,EAAE,kBAAkB;UAC5B,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOsC,cAAc,CAACrB,IAAI;IAC5B;IAEA,MAAMkB,YAAY;EACpB;AACF,CAAC;;AAED;AACA,OAAO,MAAMK,GAAG,GAAG,MAAAA,CAAU1B,GAAW,EAAEG,IAAU,EAAEP,MAA2B,KAAiB;EAChG,IAAI;IACF,MAAMb,QAA0B,GAAG,MAAMM,SAAS,CAACqC,GAAG,CAAC1B,GAAG,EAAEG,IAAI,EAAEP,MAAM,CAAC;IACzE,OAAOb,QAAQ,CAACoB,IAAI;EACtB,CAAC,CAAC,OAAOkB,YAAY,EAAE;IACrBzC,OAAO,CAACQ,KAAK,CAAC,8CAA8CY,GAAG,GAAG,EAAEqB,YAAY,CAAC;;IAEjF;IACA,IAAIC,SAAwB,GAAG,IAAI;IAEnC,IAAItB,GAAG,CAACuB,UAAU,CAAC,wBAAwB,CAAC,IAAIvB,GAAG,CAACuB,UAAU,CAAC,iBAAiB,CAAC,IAAIvB,GAAG,CAACuB,UAAU,CAAC,iBAAiB,CAAC,EAAE;MACtHD,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;IAC3C,CAAC,MAAM,IAAIA,GAAG,CAACuB,UAAU,CAAC,uBAAuB,CAAC,EAAE;MAClDD,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;IAC3C,CAAC,MAAM,IAAIA,GAAG,CAACuB,UAAU,CAAC,eAAe,CAAC,EAAE;MAC1CD,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;IAC3C,CAAC,MAAM,IAAIA,GAAG,CAACuB,UAAU,CAAC,UAAU,CAAC,IAAIvB,GAAG,CAACuB,UAAU,CAAC,mBAAmB,CAAC,EAAE;MAC5ED,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;IAC3C;IAEA,IAAIsB,SAAS,EAAE;MACb,MAAME,cAAc,GAAG,MAAMrD,KAAK,CAACuD,GAAG,CAACJ,SAAS,EAAEnB,IAAI,EAAE;QACtD,GAAGP,MAAM;QACTV,OAAO,EAAE;UACP,IAAGU,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEV,OAAO;UAClB,QAAQ,EAAE,kBAAkB;UAC5B,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOsC,cAAc,CAACrB,IAAI;IAC5B;IAEA,MAAMkB,YAAY;EACpB;AACF,CAAC;;AAED;AACA,OAAO,MAAMM,GAAG,GAAG,MAAAA,CAAU3B,GAAW,EAAEJ,MAA2B,KAAiB;EACpF,IAAI;IACF,MAAMb,QAA0B,GAAG,MAAMM,SAAS,CAACuC,MAAM,CAAC5B,GAAG,EAAEJ,MAAM,CAAC;IACtE,OAAOb,QAAQ,CAACoB,IAAI;EACtB,CAAC,CAAC,OAAOkB,YAAY,EAAE;IACrBzC,OAAO,CAACQ,KAAK,CAAC,iDAAiDY,GAAG,GAAG,EAAEqB,YAAY,CAAC;;IAEpF;IACA,IAAIC,SAAwB,GAAG,IAAI;IAEnC,IAAItB,GAAG,CAACuB,UAAU,CAAC,wBAAwB,CAAC,IAAIvB,GAAG,CAACuB,UAAU,CAAC,iBAAiB,CAAC,IAAIvB,GAAG,CAACuB,UAAU,CAAC,iBAAiB,CAAC,EAAE;MACtHD,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;IAC3C,CAAC,MAAM,IAAIA,GAAG,CAACuB,UAAU,CAAC,uBAAuB,CAAC,EAAE;MAClDD,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;IAC3C,CAAC,MAAM,IAAIA,GAAG,CAACuB,UAAU,CAAC,eAAe,CAAC,EAAE;MAC1CD,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;IAC3C,CAAC,MAAM,IAAIA,GAAG,CAACuB,UAAU,CAAC,UAAU,CAAC,IAAIvB,GAAG,CAACuB,UAAU,CAAC,mBAAmB,CAAC,EAAE;MAC5ED,SAAS,GAAG,wBAAwBtB,GAAG,EAAE;IAC3C;IAEA,IAAIsB,SAAS,EAAE;MACb,MAAME,cAAc,GAAG,MAAMrD,KAAK,CAACyD,MAAM,CAACN,SAAS,EAAE;QACnD,GAAG1B,MAAM;QACTV,OAAO,EAAE;UACP,IAAGU,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEV,OAAO;UAClB,QAAQ,EAAE,kBAAkB;UAC5B,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOsC,cAAc,CAACrB,IAAI;IAC5B;IAEA,MAAMkB,YAAY;EACpB;AACF,CAAC;AAED,eAAehC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}