import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
} from '@mui/material';
import { Customer } from '../../models';
import { customerService } from '../../services/customer/customerService';
import { Loading<PERSON>pinner, <PERSON>rror<PERSON><PERSON><PERSON>, Success<PERSON>lert } from '../common';

interface CustomerFormProps {
  customer?: Customer;
  onSave: (customer: Customer) => void;
  onCancel: () => void;
}

const CustomerForm: React.FC<CustomerFormProps> = ({ customer, onSave, onCancel }) => {
  const [formData, setFormData] = useState<Partial<Customer>>(
    customer || {
      fullName: '',
      companyName: '',
      phoneNumber: '',
      email: '',
      address: '',
    }
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const validateForm = (): boolean => {
    if (!formData.fullName) {
      setError('Vui lòng nhập tên khách hàng');
      return false;
    }

    if (!formData.phoneNumber) {
      setError('Vui lòng nhập số điện thoại');
      return false;
    }

    if (!formData.address) {
      setError('Vui lòng nhập địa chỉ');
      return false;
    }

    // Kiểm tra định dạng email nếu có
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setError('Email không hợp lệ');
      return false;
    }

    // Kiểm tra định dạng số điện thoại
    if (!/^\d{10}$/.test(formData.phoneNumber)) {
      setError('Số điện thoại phải có 10 chữ số');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      let savedCustomer: Customer;

      if (customer?.id) {
        // Cập nhật khách hàng hiện có
        savedCustomer = await customerService.updateCustomer(formData as Customer);
        setSuccess('Cập nhật khách hàng thành công!');
      } else {
        // Tạo khách hàng mới
        savedCustomer = await customerService.createCustomer(formData as Customer);
        setSuccess('Thêm khách hàng thành công!');
      }

      // Chờ một chút để hiển thị thông báo thành công
      setTimeout(() => {
        onSave(savedCustomer);
      }, 1000);
    } catch (err: any) {
      setError(err.message || 'Đã xảy ra lỗi khi lưu thông tin khách hàng');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
      {error && <ErrorAlert message={error} />}
      {success && <SuccessAlert message={success} />}
      {loading && <LoadingSpinner />}

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 8px)' } }}>
            <TextField
              fullWidth
              label="Tên khách hàng"
              name="fullName"
              value={formData.fullName || ''}
              onChange={handleInputChange}
              required
            />
          </Box>
          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 8px)' } }}>
            <TextField
              fullWidth
              label="Tên công ty"
              name="companyName"
              value={formData.companyName || ''}
              onChange={handleInputChange}
            />
          </Box>
        </Box>

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 8px)' } }}>
            <TextField
              fullWidth
              label="Số điện thoại"
              name="phoneNumber"
              value={formData.phoneNumber || ''}
              onChange={handleInputChange}
              required
              sx={{ '& input': { maxLength: 10 } }}
              helperText="Số điện thoại phải có 10 chữ số"
            />
          </Box>
          <Box sx={{ width: { xs: '100%', md: 'calc(50% - 8px)' } }}>
            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={formData.email || ''}
              onChange={handleInputChange}
            />
          </Box>
        </Box>

        <Box sx={{ width: '100%' }}>
          <TextField
            fullWidth
            label="Địa chỉ"
            name="address"
            value={formData.address || ''}
            onChange={handleInputChange}
            required
            multiline
            rows={2}
          />
        </Box>
      </Box>

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
        <Button
          variant="outlined"
          color="primary"
          onClick={onCancel}
          disabled={loading}
        >
          Hủy
        </Button>
        <Button
          type="submit"
          variant="contained"
          color="primary"
          disabled={loading}
        >
          {customer?.id ? 'Cập nhật' : 'Thêm mới'}
        </Button>
      </Box>
    </Box>
  );
};

export default CustomerForm;
