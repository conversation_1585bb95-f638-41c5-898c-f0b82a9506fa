import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Divider,
  Card,
  CardContent,
  Grid,
  Chip,
  useTheme,
  Tooltip,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import WorkIcon from '@mui/icons-material/Work';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import DateRangeIcon from '@mui/icons-material/DateRange';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { JobDetail, JobCategory, WorkShift } from '../../models';
import WorkShiftForm from './WorkShiftForm';
import { jobCategoryService } from '../../services/job/jobCategoryService';
import { formatDateForInput, formatDateLocalized } from '../../utils/dateUtils';
import { ConfirmDialog, DatePickerField } from '../common';

interface JobDetailFormProps {
  jobDetail: Partial<JobDetail>;
  onChange: (jobDetail: Partial<JobDetail>) => void;
  onDelete?: () => void;
  showDelete?: boolean;
}

const JobDetailForm: React.FC<JobDetailFormProps> = ({
  jobDetail,
  onChange,
  onDelete,
  showDelete = false,
}) => {
  const [jobCategories, setJobCategories] = useState<JobCategory[]>([]);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const theme = useTheme();

  useEffect(() => {
    const fetchJobCategories = async () => {
      try {
        const data = await jobCategoryService.getAllJobCategories();
        setJobCategories(data);
      } catch (error) {
        console.error('Error fetching job categories:', error);
      }
    };

    fetchJobCategories();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onChange({
      ...jobDetail,
      [name]: value,
    });
  };

  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;

    // If selecting a job category, find the name for display
    if (name === 'jobCategoryId') {
      const selectedCategory = jobCategories.find(cat => cat.id === value);
      onChange({
        ...jobDetail,
        [name]: value,
        jobCategoryName: selectedCategory?.name
      });
    } else {
      onChange({
        ...jobDetail,
        [name]: value,
      });
    }
  };

  const handleWorkShiftChange = (index: number, workShift: Partial<WorkShift>) => {
    const updatedWorkShifts = [...(jobDetail.workShifts || [])];
    updatedWorkShifts[index] = workShift as WorkShift;

    onChange({
      ...jobDetail,
      workShifts: updatedWorkShifts,
    });
  };

  const handleAddWorkShift = () => {
    const newWorkShift: WorkShift = {
      startTime: '',
      endTime: '',
      numberOfWorkers: 1,
      salary: 0,
      workingDays: '',
    };

    onChange({
      ...jobDetail,
      workShifts: [...(jobDetail.workShifts || []), newWorkShift],
    });
  };

  const handleDeleteWorkShift = (index: number) => {
    const updatedWorkShifts = [...(jobDetail.workShifts || [])];
    updatedWorkShifts.splice(index, 1);

    onChange({
      ...jobDetail,
      workShifts: updatedWorkShifts,
    });
  };

  return (
    <Paper
      elevation={3}
      sx={{
        p: 3,
        mb: 3,
        borderRadius: '8px',
        border: '1px solid #e0e0e0',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '6px',
          background: theme.palette.secondary.main,
        }
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <WorkIcon sx={{ mr: 1, color: theme.palette.secondary.main }} />
          <Typography variant="h5" sx={{ fontWeight: 'bold', color: theme.palette.secondary.main }}>
            Chi tiết Công việc
          </Typography>
        </Box>
        {showDelete && onDelete && (
          <Tooltip title="Xóa công việc này">
            <IconButton
              color="error"
              onClick={() => setConfirmDialogOpen(true)}
              sx={{
                border: '1px solid',
                borderColor: theme.palette.error.main,
                '&:hover': {
                  backgroundColor: theme.palette.error.light,
                }
              }}
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        )}

        <ConfirmDialog
          open={confirmDialogOpen}
          title="Xác nhận xóa"
          message="Bạn có chắc chắn muốn xóa chi tiết công việc này không? Hành động này không thể hoàn tác."
          onConfirm={() => {
            if (onDelete) onDelete();
            setConfirmDialogOpen(false);
          }}
          onCancel={() => setConfirmDialogOpen(false)}
          severity="warning"
        />
      </Box>

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
        <Box sx={{ width: { xs: '100%', md: '48%' } }}>
          <Card variant="outlined" sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                <WorkIcon sx={{ mr: 1 }} />
                Loại Công việc
              </Typography>
              <FormControl fullWidth>
                <InputLabel id="job-category-label">Chọn loại công việc</InputLabel>
                <Select
                  labelId="job-category-label"
                  id="jobCategoryId"
                  name="jobCategoryId"
                  value={jobDetail.jobCategoryId || ''}
                  label="Chọn loại công việc"
                  onChange={handleSelectChange}
                  required
                >
                  {jobCategories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </CardContent>
          </Card>
        </Box>

        <Box sx={{ width: { xs: '100%', md: '48%' } }}>
          <Card variant="outlined" sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                <LocationOnIcon sx={{ mr: 1 }} />
                Địa điểm làm việc
              </Typography>
              <TextField
                fullWidth
                label="Nhập địa điểm làm việc cụ thể"
                name="workLocation"
                value={jobDetail.workLocation || ''}
                onChange={handleInputChange}
                placeholder="Địa chỉ cụ thể nơi thực hiện công việc"
                required
              />
            </CardContent>
          </Card>
        </Box>

        <Box sx={{ width: { xs: '100%', md: '48%' } }}>
          <Card variant="outlined" sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                <DateRangeIcon sx={{ mr: 1 }} />
                Thời gian thực hiện
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                  <DatePickerField
                    label="Ngày bắt đầu"
                    value={jobDetail.startDate || ''}
                    onChange={(date) => {
                      onChange({
                        ...jobDetail,
                        startDate: date
                      });
                    }}
                    required
                  />
                </Box>
                <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                  <DatePickerField
                    label="Ngày kết thúc"
                    value={jobDetail.endDate || ''}
                    onChange={(date) => {
                      onChange({
                        ...jobDetail,
                        endDate: date
                      });
                    }}
                    required
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>

      <Divider sx={{ my: 3 }} />

      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <AccessTimeIcon sx={{ mr: 1, color: theme.palette.info.main }} />
        <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.info.main }}>
          Ca làm việc
        </Typography>
      </Box>

      {(jobDetail.workShifts || []).map((workShift, index) => (
        <WorkShiftForm
          key={index}
          workShift={workShift}
          jobDetail={jobDetail}
          onChange={(updatedWorkShift) => handleWorkShiftChange(index, updatedWorkShift)}
          onDelete={() => handleDeleteWorkShift(index)}
          showDelete={true}
          shiftIndex={index}
        />
      ))}

      <Button
        variant="outlined"
        startIcon={<AddIcon />}
        onClick={handleAddWorkShift}
        sx={{
          mt: 2,
          borderColor: theme.palette.info.main,
          color: theme.palette.info.main,
          '&:hover': {
            backgroundColor: theme.palette.info.light,
            borderColor: theme.palette.info.main,
          }
        }}
      >
        Thêm Ca làm việc
      </Button>
    </Paper>
  );
};

export default JobDetailForm;
